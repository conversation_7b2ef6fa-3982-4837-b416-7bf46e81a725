/**
 * K线数据基础功能测试
 */

require('dotenv').config();
process.env.USE_MONGODB = 'true';

const mongoose = require('mongoose');

// 连接MongoDB数据库
async function connectDB() {
  try {
    const mongoURI = process.env.MONGODB_URI || 'mongodb://localhost:27017/crypto-trading-platform';
    await mongoose.connect(mongoURI);
    console.log('✅ MongoDB连接成功');
    return true;
  } catch (error) {
    console.error('❌ MongoDB连接失败:', error.message);
    return false;
  }
}

// 测试K线数据模型
async function testKlineDataModel() {
  console.log('\n🧪 测试K线数据模型...');
  
  try {
    const KlineData = require('./models/KlineData');
    
    // 创建测试数据
    const testKline = new KlineData({
      exchange: 'binance',
      symbol: 'BTCUSDT',
      timeframe: '1h',
      timestamp: Date.now(),
      open: 50000,
      high: 51000,
      low: 49500,
      close: 50500,
      volume: 1000
    });

    // 保存测试数据
    await testKline.save();
    console.log('✅ K线数据模型保存成功');

    // 查询测试数据
    const found = await KlineData.findOne({ 
      exchange: 'binance', 
      symbol: 'BTCUSDT', 
      timeframe: '1h' 
    });
    
    if (found) {
      console.log('✅ K线数据查询成功:', found.close);
    } else {
      console.log('❌ K线数据查询失败');
      return false;
    }

    // 清理测试数据
    await KlineData.deleteOne({ _id: testKline._id });
    console.log('✅ 测试数据清理完成');

    return true;
  } catch (error) {
    console.error('❌ K线数据模型测试失败:', error);
    return false;
  }
}

// 测试K线数据服务
async function testKlineDataService() {
  console.log('\n🧪 测试K线数据服务...');
  
  try {
    const klineDataService = require('./services/klineDataService');
    
    // 模拟K线数据
    const mockKlines = [
      {
        timestamp: Date.now() - 3600000,
        open: 50000,
        high: 51000,
        low: 49500,
        close: 50500,
        volume: 1000
      },
      {
        timestamp: Date.now(),
        open: 50500,
        high: 51500,
        low: 50000,
        close: 51000,
        volume: 1200
      }
    ];

    // 测试存储功能
    const storeResult = await klineDataService.storeKlineData('binance', 'BTCUSDT', '1h', mockKlines);
    console.log('✅ 存储结果:', storeResult);

    // 测试查询功能
    const queryResult = await klineDataService.getKlineDataFromDB(
      'binance', 
      'BTCUSDT', 
      '1h', 
      Date.now() - 7200000,
      Date.now() + 3600000
    );
    console.log('✅ 查询结果:', queryResult.length, '条记录');

    return true;
  } catch (error) {
    console.error('❌ K线数据服务测试失败:', error);
    return false;
  }
}

// 测试数据收集服务配置
async function testDataCollectionService() {
  console.log('\n🧪 测试数据收集服务...');
  
  try {
    const dataCollectionService = require('./services/dataCollectionService');
    
    // 获取服务状态
    const status = dataCollectionService.getStatus();
    console.log('✅ 服务状态:', {
      isRunning: status.isRunning,
      configSymbols: status.config.symbols.length,
      configTimeframes: status.config.timeframes.length
    });

    // 测试配置更新
    const newConfig = {
      symbols: ['BTCUSDT', 'ETHUSDT'],
      timeframes: ['1h'],
      exchanges: ['binance'],
      autoCollection: false
    };

    dataCollectionService.updateConfig(newConfig);
    console.log('✅ 配置更新成功');

    return true;
  } catch (error) {
    console.error('❌ 数据收集服务测试失败:', error);
    return false;
  }
}

// 主测试函数
async function runTests() {
  console.log('🚀 开始K线数据基础功能测试\n');
  console.log('=' .repeat(50));

  // 连接数据库
  const dbConnected = await connectDB();
  if (!dbConnected) {
    console.log('❌ 无法连接数据库，退出测试');
    return;
  }

  let passedTests = 0;
  let totalTests = 0;

  // 运行测试
  const tests = [
    { name: 'K线数据模型', func: testKlineDataModel },
    { name: 'K线数据服务', func: testKlineDataService },
    { name: '数据收集服务', func: testDataCollectionService }
  ];

  for (const test of tests) {
    totalTests++;
    console.log(`\n🧪 运行测试: ${test.name}`);
    try {
      const success = await test.func();
      if (success) {
        passedTests++;
        console.log(`✅ ${test.name} 测试通过`);
      } else {
        console.log(`❌ ${test.name} 测试失败`);
      }
    } catch (error) {
      console.error(`❌ ${test.name} 测试异常:`, error.message);
    }
  }

  // 测试总结
  console.log('\n' + '=' .repeat(50));
  console.log(`📊 测试总结: ${passedTests}/${totalTests} 项测试通过`);
  
  if (passedTests === totalTests) {
    console.log('🎉 所有基础功能测试通过！');
  } else {
    console.log('⚠️ 部分测试失败，请检查相关功能');
  }

  // 关闭数据库连接
  await mongoose.connection.close();
  console.log('📝 数据库连接已关闭');
}

// 运行测试
if (require.main === module) {
  runTests().catch(error => {
    console.error('测试运行失败:', error);
    process.exit(1);
  });
}

module.exports = { runTests };
