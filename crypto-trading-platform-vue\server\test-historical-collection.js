/**
 * 测试历史数据收集功能
 */

const mongoose = require('mongoose');
const historicalDataCollector = require('./services/historicalDataCollector');
const binancePythonTradeService = require('./services/binancePythonTradeService');

// 连接MongoDB数据库
async function connectDB() {
  try {
    const mongoURI = process.env.MONGODB_URI || 'mongodb://localhost:27017/crypto-trading-platform';
    await mongoose.connect(mongoURI);
    console.log('✅ MongoDB连接成功');
    return true;
  } catch (error) {
    console.error('❌ MongoDB连接失败:', error.message);
    return false;
  }
}

async function testHistoricalDataCollection() {
  console.log('🧪 开始测试历史数据收集功能...\n');

  try {
    // 1. 测试数据库连接
    console.log('1️⃣ 测试数据库连接...');
    await connectDB();
    console.log('✅ 数据库连接成功\n');

    // 2. 测试Python脚本历史数据获取
    console.log('2️⃣ 测试Python脚本历史数据获取...');
    await testPythonHistoricalKlines();

    // 3. 测试历史数据收集器配置
    console.log('3️⃣ 测试历史数据收集器配置...');
    testCollectorConfig();

    // 4. 测试小范围历史数据收集
    console.log('4️⃣ 测试小范围历史数据收集...');
    await testSmallRangeCollection();

    console.log('\n🎉 所有测试完成！');

  } catch (error) {
    console.error('❌ 测试失败:', error);
  }
}

async function testPythonHistoricalKlines() {
  try {
    // 测试获取2024年8月1日的1小时数据
    const startTime = new Date('2024-08-01T00:00:00Z').getTime();
    const endTime = new Date('2024-08-01T23:59:59Z').getTime();

    console.log(`   📊 测试时间范围: ${new Date(startTime)} 到 ${new Date(endTime)}`);

    const result = await binancePythonTradeService.getBinanceHistoricalKlines(
      'BTCUSDT', '1h', 24, startTime, endTime
    );

    if (result.success && result.klines) {
      console.log(`   ✅ 成功获取 ${result.klines.length} 条历史数据`);
      console.log(`   📈 第一条: ${new Date(result.klines[0].timestamp)} 价格: ${result.klines[0].close}`);
      console.log(`   📈 最后一条: ${new Date(result.klines[result.klines.length-1].timestamp)} 价格: ${result.klines[result.klines.length-1].close}`);
    } else {
      console.log(`   ❌ 获取历史数据失败: ${result.error}`);
    }

    console.log('');
  } catch (error) {
    console.error('   ❌ Python脚本测试失败:', error.message);
  }
}

function testCollectorConfig() {
  try {
    const status = historicalDataCollector.getStatus();
    console.log('   ✅ 收集器状态:', {
      isRunning: status.isRunning,
      startDate: status.config.startDate,
      symbolsCount: status.config.symbols.length,
      timeframesCount: status.config.timeframes.length
    });
    console.log('');
  } catch (error) {
    console.error('   ❌ 配置测试失败:', error.message);
  }
}

async function testSmallRangeCollection() {
  try {
    console.log('   🚀 开始小范围测试收集（仅BTCUSDT，2024年8月1-2日）...');

    // 配置小范围测试
    const testOptions = {
      startDate: '2024-08-01',
      symbols: ['BTCUSDT'],
      timeframes: ['1h'],
      batchSize: 100,
      batchDelay: 100,
      monthDelay: 500
    };

    // 计算测试时间范围
    const startDate = new Date(testOptions.startDate);
    const endDate = new Date('2024-08-02');
    const months = getTestMonthsRange(startDate, endDate);

    console.log(`   📅 测试月份范围: ${months.length} 个月`);

    // 启动收集（异步）
    const collectionPromise = historicalDataCollector.startCollection(testOptions);

    // 等待一段时间后检查状态
    setTimeout(() => {
      const status = historicalDataCollector.getStatus();
      console.log(`   📊 收集状态: ${status.isRunning ? '运行中' : '已停止'}`);
    }, 2000);

    // 等待收集完成或超时
    const timeoutPromise = new Promise((resolve) => {
      setTimeout(() => {
        historicalDataCollector.stop();
        resolve('timeout');
      }, 30000); // 30秒超时
    });

    const result = await Promise.race([collectionPromise, timeoutPromise]);

    if (result === 'timeout') {
      console.log('   ⏰ 测试超时，停止收集');
    } else {
      console.log('   ✅ 小范围收集测试完成');
    }

    console.log('');
  } catch (error) {
    console.error('   ❌ 小范围收集测试失败:', error.message);
  }
}

function getTestMonthsRange(startDate, endDate) {
  const months = [];
  const current = new Date(startDate.getFullYear(), startDate.getMonth(), 1);
  const end = new Date(endDate.getFullYear(), endDate.getMonth(), 1);

  while (current <= end) {
    months.push({
      year: current.getFullYear(),
      month: current.getMonth() + 1
    });
    current.setMonth(current.getMonth() + 1);
  }

  return months;
}

// 运行测试
if (require.main === module) {
  testHistoricalDataCollection().then(() => {
    console.log('测试脚本执行完成');
    process.exit(0);
  }).catch(error => {
    console.error('测试脚本执行失败:', error);
    process.exit(1);
  });
}

module.exports = {
  testHistoricalDataCollection,
  testPythonHistoricalKlines,
  testCollectorConfig,
  testSmallRangeCollection
};
