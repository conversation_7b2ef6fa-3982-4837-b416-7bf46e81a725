#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
币安交易脚本 - 使用币安官方Python SDK进行交易
"""

import os
import sys
import json
import time
import logging
from datetime import datetime
from binance.client import Client
from binance.exceptions import BinanceAPIException, BinanceOrderException

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("binance_trader.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("binance_trader")

# 币安API密钥存储路径
BINANCE_API_KEYS_FILE = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'data', 'binance_api_keys.json')

def load_binance_api_keys(user_id='default'):
    """
    加载币安API密钥

    Args:
        user_id (str): 用户ID

    Returns:
        dict: 币安API密钥信息
    """
    try:
        # 确保目录存在
        os.makedirs(os.path.dirname(BINANCE_API_KEYS_FILE), exist_ok=True)

        # 如果文件不存在，创建一个空的JSON文件
        if not os.path.exists(BINANCE_API_KEYS_FILE):
            with open(BINANCE_API_KEYS_FILE, 'w', encoding='utf-8') as f:
                json.dump({}, f)
            return None

        # 读取币安API密钥
        with open(BINANCE_API_KEYS_FILE, 'r', encoding='utf-8') as f:
            api_keys = json.load(f)

        user_keys = api_keys.get(user_id)
        if user_keys and user_keys.get('exchange') == 'binance':
            return user_keys
        return None
    except Exception as e:
        logger.error(f"加载币安API密钥失败: {e}")
        return None

def create_binance_client(api_key=None, secret_key=None):
    """
    创建币安交易客户端

    Args:
        api_key (str): 币安API Key
        secret_key (str): 币安Secret Key

    Returns:
        binance.client.Client: 币安交易客户端
    """
    try:
        # 如果没有提供API密钥，尝试从文件加载
        if not all([api_key, secret_key]):
            keys = load_binance_api_keys()
            if not keys:
                logger.error("未找到币安API密钥")
                return None

            api_key = keys.get('apiKey')
            secret_key = keys.get('secretKey')

        # 创建币安客户端
        client = Client(api_key, secret_key)

        # 测试连接
        client.ping()
        logger.info("币安客户端连接成功")

        return client
    except Exception as e:
        logger.error(f"创建币安客户端失败: {e}")
        return None

def create_binance_public_client():
    """
    创建币安公开数据客户端（不需要API密钥）

    Returns:
        binance.client.Client: 币安公开数据客户端
    """
    try:
        # 创建不需要API密钥的客户端，用于获取公开数据
        client = Client()

        # 测试连接（公开API不需要认证）
        try:
            client.ping()
            logger.info("币安公开数据客户端连接成功")
        except Exception as ping_error:
            # ping失败不影响公开数据获取
            logger.warning(f"币安ping测试失败，但公开客户端仍可用: {ping_error}")

        return client
    except Exception as e:
        logger.error(f"创建币安公开数据客户端失败: {e}")
        return None

def create_binance_client_for_user(user_id='default'):
    """
    为指定用户创建币安交易客户端

    Args:
        user_id (str): 用户ID

    Returns:
        binance.client.Client: 币安交易客户端
    """
    try:
        # 加载指定用户的API密钥
        keys = load_binance_api_keys(user_id)
        if not keys:
            logger.error(f"未找到用户 {user_id} 的币安API密钥")
            return None

        api_key = keys.get('apiKey')
        secret_key = keys.get('secretKey')

        if not all([api_key, secret_key]):
            logger.error(f"用户 {user_id} 的币安API密钥不完整")
            return None

        # 创建币安客户端
        client = Client(api_key, secret_key)

        # 测试连接
        client.ping()
        logger.info(f"用户 {user_id} 的币安客户端连接成功")

        return client
    except Exception as e:
        logger.error(f"为用户 {user_id} 创建币安客户端失败: {e}")
        return None

def get_binance_account_balance(user_id='default', client=None):
    """
    获取币安账户余额（包括现货和合约）

    Args:
        user_id (str): 用户ID
        client (binance.client.Client): 币安交易客户端

    Returns:
        dict: 账户余额信息
    """
    try:
        if not client:
            client = create_binance_client_for_user(user_id)
            if not client:
                return {"error": "创建币安客户端失败"}

        # 获取现货账户信息
        spot_account_info = client.get_account()

        # 提取现货有余额的资产
        spot_assets = {}
        for balance in spot_account_info['balances']:
            free = float(balance['free'])
            locked = float(balance['locked'])
            total = free + locked

            if total > 0:
                spot_assets[balance['asset']] = {
                    'free': free,
                    'locked': locked,
                    'total': total
                }

        # 获取合约账户信息
        futures_assets = {}
        try:
            futures_account_info = client.futures_account()

            # 提取合约有余额的资产
            for balance in futures_account_info['assets']:
                wallet_balance = float(balance['walletBalance'])
                unrealized_profit = float(balance['unrealizedProfit'])
                margin_balance = float(balance['marginBalance'])

                if wallet_balance > 0 or unrealized_profit != 0:
                    futures_assets[balance['asset']] = {
                        'walletBalance': wallet_balance,
                        'unrealizedProfit': unrealized_profit,
                        'marginBalance': margin_balance,
                        'availableBalance': float(balance['availableBalance'])
                    }

            # 获取合约持仓信息
            positions = []
            for position in futures_account_info['positions']:
                position_amt = float(position['positionAmt'])
                if position_amt != 0:
                    positions.append({
                        'symbol': position['symbol'],
                        'positionAmt': position_amt,
                        'entryPrice': float(position['entryPrice']),
                        'markPrice': float(position['markPrice']),
                        'unRealizedProfit': float(position['unRealizedProfit']),
                        'percentage': float(position['percentage']) if position['percentage'] else 0,
                        'positionSide': position['positionSide']
                    })

        except Exception as futures_error:
            logger.warning(f"获取合约账户信息失败: {futures_error}")
            # 如果合约账户获取失败，继续返回现货信息

        return {
            "success": True,
            "balance": {
                "spot": spot_assets,
                "futures": futures_assets,
                "positions": positions if 'positions' in locals() else []
            }
        }
    except BinanceAPIException as e:
        logger.error(f"获取币安账户余额失败: {e}")
        return {"error": f"获取币安账户余额失败: {str(e)}"}
    except Exception as e:
        logger.error(f"获取币安账户余额失败: {e}")
        return {"error": f"获取币安账户余额失败: {str(e)}"}

def execute_binance_trade(symbol, side, amount, price=None, client=None):
    """
    执行币安交易

    Args:
        symbol (str): 交易对，如 'BTCUSDT'
        side (str): 交易方向，'BUY' 或 'SELL'
        amount (float): 交易数量
        price (float, optional): 限价单价格，如果为None则为市价单
        client (binance.client.Client, optional): 币安交易客户端

    Returns:
        dict: 交易结果
    """
    try:
        if not client:
            client = create_binance_client()
            if not client:
                return {"error": "创建币安客户端失败"}

        # 确定订单类型
        if price:
            # 限价单
            order = client.order_limit(
                symbol=symbol,
                side=side,
                quantity=amount,
                price=str(price)
            )
        else:
            # 市价单
            if side == 'BUY':
                # 市价买入，使用金额
                order = client.order_market_buy(
                    symbol=symbol,
                    quoteOrderQty=amount
                )
            else:
                # 市价卖出，使用数量
                order = client.order_market_sell(
                    symbol=symbol,
                    quantity=amount
                )

        return {
            "success": True,
            "order": order
        }
    except BinanceOrderException as e:
        logger.error(f"执行币安交易失败: {e}")
        return {"error": f"执行币安交易失败: {str(e)}"}
    except BinanceAPIException as e:
        logger.error(f"执行币安交易失败: {e}")
        return {"error": f"执行币安交易失败: {str(e)}"}
    except Exception as e:
        logger.error(f"执行币安交易失败: {e}")
        return {"error": f"执行币安交易失败: {str(e)}"}

def get_binance_market_price(symbol, client=None):
    """
    获取币安市场价格

    Args:
        symbol (str): 交易对，如 'BTCUSDT'
        client (binance.client.Client, optional): 币安交易客户端

    Returns:
        dict: 市场价格信息
    """
    try:
        if not client:
            client = create_binance_client()
            if not client:
                return {"error": "创建币安客户端失败"}

        # 获取24小时价格变动统计
        ticker = client.get_ticker(symbol=symbol)

        return {
            "success": True,
            "ticker": {
                "symbol": ticker['symbol'],
                "price": float(ticker['lastPrice']),
                "bid": float(ticker['bidPrice']),
                "ask": float(ticker['askPrice']),
                "high": float(ticker['highPrice']),
                "low": float(ticker['lowPrice']),
                "volume": float(ticker['volume']),
                "change": float(ticker['priceChange']),
                "changePercent": float(ticker['priceChangePercent']),
                "timestamp": int(ticker['closeTime'])
            }
        }
    except BinanceAPIException as e:
        logger.error(f"获取币安市场价格失败: {e}")
        return {"error": f"获取币安市场价格失败: {str(e)}"}
    except Exception as e:
        logger.error(f"获取币安市场价格失败: {e}")
        return {"error": f"获取币安市场价格失败: {str(e)}"}

def get_binance_klines(symbol, interval='1h', limit=100, client=None):
    """
    获取币安K线数据

    Args:
        symbol (str): 交易对，如 'BTCUSDT'
        interval (str): K线间隔，如 '1h', '4h', '1d'
        limit (int): 获取数量
        client (binance.client.Client, optional): 币安交易客户端

    Returns:
        dict: K线数据
    """
    try:
        if not client:
            # 优先尝试创建公开数据客户端（不需要API密钥）
            client = create_binance_public_client()
            if not client:
                # 如果公开客户端创建失败，尝试使用认证客户端
                client = create_binance_client()
                if not client:
                    return {"error": "创建币安客户端失败"}

        # 获取K线数据
        klines = client.get_klines(symbol=symbol, interval=interval, limit=limit)

        # 格式化K线数据
        formatted_klines = []
        for kline in klines:
            formatted_klines.append({
                'timestamp': kline[0],
                'open': float(kline[1]),
                'high': float(kline[2]),
                'low': float(kline[3]),
                'close': float(kline[4]),
                'volume': float(kline[5])
            })

        logger.info(f"成功获取币安K线数据: {symbol} {interval} {len(formatted_klines)}条")
        return {
            "success": True,
            "klines": formatted_klines
        }
    except BinanceAPIException as e:
        logger.error(f"获取币安K线数据失败: {e}")
        return {"error": f"获取币安K线数据失败: {str(e)}"}
    except Exception as e:
        logger.error(f"获取币安K线数据失败: {e}")
        return {"error": f"获取币安K线数据失败: {str(e)}"}

def get_binance_order_history(user_id='default', symbol=None, limit=20, page=1, client=None):
    """
    获取币安历史订单

    Args:
        user_id (str): 用户ID
        symbol (str, optional): 交易对，如 'BTCUSDT'
        limit (int): 获取数量
        page (int): 页码
        client (binance.client.Client, optional): 币安交易客户端

    Returns:
        dict: 历史订单数据
    """
    try:
        if not client:
            client = create_binance_client_for_user(user_id)
            if not client:
                return {"error": f"为用户 {user_id} 创建币安客户端失败"}

        # 获取历史订单
        orders = []

        if symbol:
            # 获取指定交易对的订单
            all_orders = client.get_all_orders(symbol=symbol, limit=limit)
        else:
            # 获取所有交易对的最近订单
            # 由于币安API限制，我们需要获取账户信息来找到有交易的交易对
            account_info = client.get_account()
            traded_symbols = set()

            # 从账户余额中找到可能有交易的交易对
            for balance in account_info['balances']:
                if float(balance['free']) > 0 or float(balance['locked']) > 0:
                    asset = balance['asset']
                    if asset != 'USDT':
                        traded_symbols.add(f"{asset}USDT")

            # 限制查询的交易对数量，避免API调用过多
            traded_symbols = list(traded_symbols)[:10]

            all_orders = []
            for symbol_to_check in traded_symbols:
                try:
                    symbol_orders = client.get_all_orders(symbol=symbol_to_check, limit=5)
                    all_orders.extend(symbol_orders)
                except Exception as e:
                    logger.warning(f"获取交易对 {symbol_to_check} 的订单失败: {e}")
                    continue

            # 按时间排序，取最新的
            all_orders.sort(key=lambda x: x['time'], reverse=True)
            all_orders = all_orders[:limit]

        # 格式化订单数据
        for order in all_orders:
            # 对于市价单，如果price为0，尝试计算平均成交价格
            price = float(order['price'])
            if price == 0 and float(order['executedQty']) > 0:
                # 市价单的实际成交价格需要从cummulativeQuoteQty计算
                cumulative_quote_qty = float(order.get('cummulativeQuoteQty', 0))
                executed_qty = float(order['executedQty'])
                if executed_qty > 0:
                    price = cumulative_quote_qty / executed_qty

            # 格式化时间戳为可读格式
            order_time = datetime.fromtimestamp(order['time'] / 1000).strftime('%Y-%m-%d %H:%M:%S')

            orders.append({
                'orderId': str(order['orderId']),
                'symbol': order['symbol'],
                'side': order['side'],
                'type': order['type'],
                'origQty': float(order['origQty']),
                'executedQty': float(order['executedQty']),
                'price': price,
                'cummulativeQuoteQty': float(order.get('cummulativeQuoteQty', 0)),
                'status': order['status'],
                'time': order['time'],
                'timeString': order_time,
                'updateTime': order['updateTime']
            })

        return {
            "success": True,
            "orders": orders
        }
    except BinanceAPIException as e:
        logger.error(f"获取币安历史订单失败: {e}")
        return {"error": f"获取币安历史订单失败: {str(e)}"}
    except Exception as e:
        logger.error(f"获取币安历史订单失败: {e}")
        return {"error": f"获取币安历史订单失败: {str(e)}"}

if __name__ == "__main__":
    # 简单的命令行接口
    if len(sys.argv) < 2:
        print("用法: python binance_trader.py <command> [args...]")
        print("可用命令:")
        print("  balance [user_id] - 获取币安账户余额")
        print("  price <symbol> - 获取币安市场价格")
        print("  trade <symbol> <side> <amount> [price] - 执行币安交易")
        print("  klines <symbol> [interval] [limit] - 获取币安K线数据")
        print("  orders <user_id> [symbol] [limit] [page] - 获取币安历史订单")
        sys.exit(1)

    command = sys.argv[1]

    if command == "balance":
        user_id = sys.argv[2] if len(sys.argv) >= 3 else 'default'
        result = get_binance_account_balance(user_id)
        print(json.dumps(result, indent=2, ensure_ascii=False))

    elif command == "price" and len(sys.argv) >= 3:
        symbol = sys.argv[2]
        result = get_binance_market_price(symbol)
        print(json.dumps(result, indent=2, ensure_ascii=False))

    elif command == "trade" and len(sys.argv) >= 5:
        symbol = sys.argv[2]
        side = sys.argv[3].upper()
        amount = float(sys.argv[4])
        price = float(sys.argv[5]) if len(sys.argv) >= 6 else None
        result = execute_binance_trade(symbol, side, amount, price)
        print(json.dumps(result, indent=2, ensure_ascii=False))

    elif command == "klines":
        symbol = sys.argv[2] if len(sys.argv) >= 3 else 'BTCUSDT'
        interval = sys.argv[3] if len(sys.argv) >= 4 else '1h'
        limit = int(sys.argv[4]) if len(sys.argv) >= 5 else 100
        # 强制使用公共客户端获取K线数据（不需要API密钥）
        public_client = create_binance_public_client()
        if public_client:
            result = get_binance_klines(symbol, interval, limit, public_client)
        else:
            result = {"error": "无法创建币安公共客户端"}
        print(json.dumps(result, indent=2, ensure_ascii=False))

    elif command == "orders" and len(sys.argv) >= 3:
        user_id = sys.argv[2]
        symbol = sys.argv[3] if len(sys.argv) >= 4 and sys.argv[3] != 'undefined' else None
        limit = int(sys.argv[4]) if len(sys.argv) >= 5 else 20
        page = int(sys.argv[5]) if len(sys.argv) >= 6 else 1
        result = get_binance_order_history(user_id, symbol, limit, page)
        print(json.dumps(result, indent=2, ensure_ascii=False))

    else:
        print("无效的命令或参数不足")
        sys.exit(1)
