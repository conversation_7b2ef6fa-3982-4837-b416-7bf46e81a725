/**
 * K线数据功能测试脚本
 */

require('dotenv').config();
process.env.USE_MONGODB = 'true';

const mongoose = require('mongoose');
const klineDataService = require('./services/klineDataService');
const dataCollectionService = require('./services/dataCollectionService');

// 连接MongoDB数据库
async function connectDB() {
  try {
    const mongoURI = process.env.MONGODB_URI || 'mongodb://localhost:27017/crypto-trading-platform';
    await mongoose.connect(mongoURI);
    console.log('✅ MongoDB连接成功');
    return true;
  } catch (error) {
    console.error('❌ MongoDB连接失败:', error.message);
    return false;
  }
}

// 测试K线数据存储
async function testKlineDataStorage() {
  console.log('\n🧪 测试K线数据存储功能...');
  
  try {
    // 模拟K线数据
    const mockKlines = [
      {
        timestamp: Date.now() - 3600000, // 1小时前
        open: 50000,
        high: 51000,
        low: 49500,
        close: 50500,
        volume: 1000
      },
      {
        timestamp: Date.now() - 1800000, // 30分钟前
        open: 50500,
        high: 51500,
        low: 50000,
        close: 51000,
        volume: 1200
      },
      {
        timestamp: Date.now(), // 现在
        open: 51000,
        high: 51200,
        low: 50800,
        close: 51100,
        volume: 800
      }
    ];

    // 存储测试数据
    const result = await klineDataService.storeKlineData('binance', 'BTCUSDT', '1h', mockKlines);
    console.log('✅ 存储结果:', result);

    // 查询测试数据
    const retrievedKlines = await klineDataService.getKlineDataFromDB(
      'binance', 
      'BTCUSDT', 
      '1h', 
      Date.now() - 7200000, // 2小时前
      Date.now() + 3600000   // 1小时后
    );
    console.log('✅ 查询结果:', retrievedKlines.length, '条记录');

    return true;
  } catch (error) {
    console.error('❌ K线数据存储测试失败:', error);
    return false;
  }
}

// 测试数据统计功能
async function testDataStats() {
  console.log('\n📊 测试数据统计功能...');
  
  try {
    const stats = await klineDataService.getDataStats();
    console.log('✅ 数据统计结果:');
    stats.forEach(stat => {
      console.log(`  - ${stat._id.exchange} ${stat._id.symbol} ${stat._id.timeframe}: ${stat.count} 条记录`);
    });
    return true;
  } catch (error) {
    console.error('❌ 数据统计测试失败:', error);
    return false;
  }
}

// 测试数据收集服务配置
async function testDataCollectionConfig() {
  console.log('\n⚙️ 测试数据收集服务配置...');
  
  try {
    // 获取当前状态
    const status = dataCollectionService.getStatus();
    console.log('✅ 当前状态:', status);

    // 更新配置
    const newConfig = {
      symbols: ['BTCUSDT', 'ETHUSDT'],
      timeframes: ['1h'],
      exchanges: ['binance'],
      batchSize: 100,
      autoCollection: false // 测试时关闭自动收集
    };

    dataCollectionService.updateConfig(newConfig);
    console.log('✅ 配置更新成功');

    const updatedStatus = dataCollectionService.getStatus();
    console.log('✅ 更新后状态:', updatedStatus.config);

    return true;
  } catch (error) {
    console.error('❌ 数据收集配置测试失败:', error);
    return false;
  }
}

// 测试手动数据收集
async function testManualDataCollection() {
  console.log('\n🔄 测试手动数据收集...');
  
  try {
    // 手动收集少量数据进行测试
    const result = await dataCollectionService.manualCollect('binance', 'BTCUSDT', '1h');
    console.log('✅ 手动收集结果:', result);
    return true;
  } catch (error) {
    console.error('❌ 手动数据收集测试失败:', error);
    return false;
  }
}

// 测试回测数据获取
async function testBacktestDataRetrieval() {
  console.log('\n📈 测试回测数据获取...');
  
  try {
    const startTime = Date.now() - 24 * 3600000; // 24小时前
    const endTime = Date.now();
    
    const klines = await klineDataService.getKlineData(
      'binance',
      'BTCUSDT',
      '1h',
      startTime,
      endTime,
      24 // 24小时的数据
    );
    
    console.log('✅ 回测数据获取成功:', klines.length, '条记录');
    if (klines.length > 0) {
      console.log('  - 第一条:', new Date(klines[0].timestamp), klines[0].close);
      console.log('  - 最后一条:', new Date(klines[klines.length - 1].timestamp), klines[klines.length - 1].close);
    }
    
    return true;
  } catch (error) {
    console.error('❌ 回测数据获取测试失败:', error);
    return false;
  }
}

// 主测试函数
async function runTests() {
  console.log('🚀 开始K线数据功能测试\n');
  console.log('=' .repeat(60));

  // 连接数据库
  const dbConnected = await connectDB();
  if (!dbConnected) {
    console.log('❌ 无法连接数据库，退出测试');
    return;
  }

  // 加载数据模型
  require('./models/KlineData');

  let passedTests = 0;
  let totalTests = 0;

  // 运行各项测试
  const tests = [
    { name: 'K线数据存储', func: testKlineDataStorage },
    { name: '数据统计', func: testDataStats },
    { name: '数据收集配置', func: testDataCollectionConfig },
    { name: '手动数据收集', func: testManualDataCollection },
    { name: '回测数据获取', func: testBacktestDataRetrieval }
  ];

  for (const test of tests) {
    totalTests++;
    console.log(`\n🧪 运行测试: ${test.name}`);
    try {
      const success = await test.func();
      if (success) {
        passedTests++;
        console.log(`✅ ${test.name} 测试通过`);
      } else {
        console.log(`❌ ${test.name} 测试失败`);
      }
    } catch (error) {
      console.error(`❌ ${test.name} 测试异常:`, error.message);
    }
  }

  // 测试总结
  console.log('\n' + '=' .repeat(60));
  console.log(`📊 测试总结: ${passedTests}/${totalTests} 项测试通过`);
  
  if (passedTests === totalTests) {
    console.log('🎉 所有测试通过！K线数据功能正常工作');
  } else {
    console.log('⚠️ 部分测试失败，请检查相关功能');
  }

  // 关闭数据库连接
  await mongoose.connection.close();
  console.log('📝 数据库连接已关闭');
}

// 运行测试
if (require.main === module) {
  runTests().catch(error => {
    console.error('测试运行失败:', error);
    process.exit(1);
  });
}

module.exports = { runTests };
