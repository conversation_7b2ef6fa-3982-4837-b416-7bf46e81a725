const express = require('express');
const router = express.Router();
const klineDataService = require('../services/klineDataService');
const dataCollectionService = require('../services/dataCollectionService');
const historicalDataCollector = require('../services/historicalDataCollector');

/**
 * 获取K线数据
 * GET /api/kline-data/:exchange/:symbol/:timeframe
 */
router.get('/:exchange/:symbol/:timeframe', async (req, res) => {
  try {
    const { exchange, symbol, timeframe } = req.params;
    const { startTime, endTime, limit = 1000, userId } = req.query;

    // 参数验证
    if (!['binance', 'okx'].includes(exchange)) {
      return res.status(400).json({
        success: false,
        error: '不支持的交易所'
      });
    }

    const validTimeframes = ['1m', '3m', '5m', '15m', '30m', '1h', '2h', '4h', '6h', '12h', '1d', '1w', '1M'];
    if (!validTimeframes.includes(timeframe)) {
      return res.status(400).json({
        success: false,
        error: '不支持的时间周期'
      });
    }

    // 转换时间参数
    const start = startTime ? parseInt(startTime) : null;
    const end = endTime ? parseInt(endTime) : null;
    const limitNum = Math.min(parseInt(limit), 5000); // 最大限制5000条

    console.log(`API请求K线数据: ${exchange} ${symbol} ${timeframe}, limit=${limitNum}`);

    // 获取K线数据
    const klines = await klineDataService.getKlineData(
      exchange,
      symbol,
      timeframe,
      start,
      end,
      limitNum,
      userId
    );

    res.json({
      success: true,
      data: {
        exchange,
        symbol,
        timeframe,
        count: klines.length,
        klines
      }
    });

  } catch (error) {
    console.error('获取K线数据API错误:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * 获取数据统计信息
 * GET /api/kline-data/stats
 */
router.get('/stats', async (req, res) => {
  try {
    const stats = await klineDataService.getDataStats();

    // 格式化统计信息
    const formattedStats = stats.map(stat => ({
      exchange: stat._id.exchange,
      symbol: stat._id.symbol,
      timeframe: stat._id.timeframe,
      count: stat.count,
      dateRange: {
        start: new Date(stat.minTimestamp),
        end: new Date(stat.maxTimestamp)
      },
      lastUpdated: stat.lastUpdated
    }));

    res.json({
      success: true,
      data: {
        totalRecords: stats.reduce((sum, stat) => sum + stat.count, 0),
        stats: formattedStats
      }
    });

  } catch (error) {
    console.error('获取数据统计API错误:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * 手动触发数据收集
 * POST /api/kline-data/collect
 */
router.post('/collect', async (req, res) => {
  try {
    const { exchange, symbol, timeframe } = req.body;

    console.log('手动触发数据收集:', { exchange, symbol, timeframe });

    const result = await dataCollectionService.manualCollect(exchange, symbol, timeframe);

    res.json({
      success: true,
      data: {
        collected: result.collected,
        errors: result.errors,
        message: `收集完成: ${result.collected} 条记录，${result.errors} 个错误`
      }
    });

  } catch (error) {
    console.error('手动数据收集API错误:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * 获取数据收集服务状态
 * GET /api/kline-data/collection-status
 */
router.get('/collection-status', (req, res) => {
  try {
    const status = dataCollectionService.getStatus();

    res.json({
      success: true,
      data: status
    });

  } catch (error) {
    console.error('获取收集状态API错误:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * 启动数据收集服务
 * POST /api/kline-data/start-collection
 */
router.post('/start-collection', async (req, res) => {
  try {
    await dataCollectionService.start();

    res.json({
      success: true,
      message: '数据收集服务已启动'
    });

  } catch (error) {
    console.error('启动数据收集API错误:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * 停止数据收集服务
 * POST /api/kline-data/stop-collection
 */
router.post('/stop-collection', (req, res) => {
  try {
    dataCollectionService.stop();

    res.json({
      success: true,
      message: '数据收集服务已停止'
    });

  } catch (error) {
    console.error('停止数据收集API错误:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * 更新数据收集配置
 * PUT /api/kline-data/collection-config
 */
router.put('/collection-config', (req, res) => {
  try {
    const newConfig = req.body;
    dataCollectionService.updateConfig(newConfig);

    res.json({
      success: true,
      message: '数据收集配置已更新',
      data: dataCollectionService.getStatus().config
    });

  } catch (error) {
    console.error('更新收集配置API错误:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * 清理过期数据
 * DELETE /api/kline-data/cleanup
 */
router.delete('/cleanup', async (req, res) => {
  try {
    const { daysToKeep = 365 } = req.query;
    const days = parseInt(daysToKeep);

    if (days < 30) {
      return res.status(400).json({
        success: false,
        error: '保留天数不能少于30天'
      });
    }

    const result = await klineDataService.cleanupOldData(days);

    res.json({
      success: true,
      data: {
        deletedCount: result.deletedCount,
        message: `清理完成，删除了 ${result.deletedCount} 条过期记录`
      }
    });

  } catch (error) {
    console.error('清理数据API错误:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * 存储K线数据
 * POST /api/kline-data/store
 */
router.post('/store', async (req, res) => {
  try {
    const { exchange, symbol, timeframe, klines } = req.body;

    // 参数验证
    if (!exchange || !symbol || !timeframe || !klines) {
      return res.status(400).json({
        success: false,
        error: '缺少必要参数'
      });
    }

    if (!Array.isArray(klines)) {
      return res.status(400).json({
        success: false,
        error: 'klines必须是数组'
      });
    }

    const result = await klineDataService.storeKlineData(exchange, symbol, timeframe, klines);

    res.json({
      success: true,
      data: {
        insertedCount: result.insertedCount,
        errors: result.errors,
        message: `存储完成，插入/更新 ${result.insertedCount} 条记录`
      }
    });

  } catch (error) {
    console.error('存储K线数据API错误:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * 开始历史数据收集
 * POST /api/kline-data/start-historical-collection
 */
router.post('/start-historical-collection', async (req, res) => {
  try {
    const options = req.body || {};

    console.log('开始历史数据收集，选项:', options);

    // 异步启动收集，不等待完成
    historicalDataCollector.startCollection(options).catch(error => {
      console.error('历史数据收集过程中出错:', error);
    });

    res.json({
      success: true,
      message: '历史数据收集已启动，将在后台运行'
    });

  } catch (error) {
    console.error('启动历史数据收集API错误:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * 停止历史数据收集
 * POST /api/kline-data/stop-historical-collection
 */
router.post('/stop-historical-collection', (req, res) => {
  try {
    historicalDataCollector.stop();

    res.json({
      success: true,
      message: '历史数据收集已停止'
    });

  } catch (error) {
    console.error('停止历史数据收集API错误:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * 获取历史数据收集状态
 * GET /api/kline-data/historical-collection-status
 */
router.get('/historical-collection-status', (req, res) => {
  try {
    const status = historicalDataCollector.getStatus();

    res.json({
      success: true,
      data: status
    });

  } catch (error) {
    console.error('获取历史收集状态API错误:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

module.exports = router;
