/**
 * 测试回测功能的简化脚本
 */

console.log('开始测试回测功能...');

async function testBacktest() {
  try {
    console.log('1. 测试Python脚本直接调用...');
    
    // 测试Python脚本
    const { spawn } = require('child_process');
    const path = require('path');
    
    const pythonScript = path.join(__dirname, 'python', 'binance_trader.py');
    const args = ['klines', 'BTCUSDT', '1h', '5'];
    
    console.log(`执行: python ${pythonScript} ${args.join(' ')}`);
    
    const pythonProcess = spawn('python', [pythonScript, ...args], {
      windowsHide: true,
      env: { ...process.env, PYTHONIOENCODING: 'utf-8' }
    });
    
    let stdout = '';
    let stderr = '';
    
    pythonProcess.stdout.on('data', (data) => {
      stdout += data.toString('utf8');
    });
    
    pythonProcess.stderr.on('data', (data) => {
      stderr += data.toString('utf8');
    });
    
    pythonProcess.on('close', (code) => {
      console.log(`Python脚本退出码: ${code}`);
      if (code === 0) {
        console.log('Python脚本输出:', stdout);
        try {
          const result = JSON.parse(stdout);
          console.log('✅ Python脚本成功返回数据:', result.success, '条数:', result.klines?.length);
          
          // 继续测试Node.js服务
          testNodeServices(result);
        } catch (error) {
          console.error('❌ 解析Python输出失败:', error.message);
        }
      } else {
        console.error('❌ Python脚本执行失败:', stderr);
      }
    });
    
    pythonProcess.on('error', (error) => {
      console.error('❌ 启动Python脚本失败:', error.message);
    });
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  }
}

async function testNodeServices(pythonResult) {
  try {
    console.log('\n2. 测试Node.js服务...');
    
    // 测试binancePythonTradeService
    console.log('测试binancePythonTradeService...');
    const binancePythonTradeService = require('./services/binancePythonTradeService');
    
    const result = await binancePythonTradeService.getBinanceKlines('BTCUSDT', '1h', 5);
    console.log('✅ binancePythonTradeService结果:', result.success, '条数:', result.klines?.length);
    
    // 测试klineDataService
    console.log('测试klineDataService...');
    const klineDataService = require('./services/klineDataService');
    
    const klines = await klineDataService.getKlineDataFromAPI('binance', 'BTCUSDT', '1h', 5, 'default');
    console.log('✅ klineDataService结果:', klines.length, '条数据');
    
    // 测试回测服务
    console.log('测试backtestService...');
    const backtestService = require('./services/backtestService');
    
    const backtestResult = await backtestService.runBacktest({
      exchange: 'binance',
      strategyId: 'binance_futures2',
      symbol: 'BTCUSDT',
      timeframe: '1h',
      startDate: '2024-12-30',
      endDate: '2024-12-31',
      initialCapital: 10000,
      feeRate: 0.1,
      strategyParams: {},
      userId: 'default'
    });
    
    console.log('✅ 回测结果:', backtestResult.success ? '成功' : '失败');
    if (backtestResult.success) {
      console.log('回测指标:', {
        totalReturn: backtestResult.results.totalReturn,
        winRate: backtestResult.results.winRate,
        totalTrades: backtestResult.results.totalTrades
      });
    } else {
      console.log('回测错误:', backtestResult.error);
    }
    
  } catch (error) {
    console.error('❌ Node.js服务测试失败:', error.message);
    console.error('错误堆栈:', error.stack);
  }
}

// 启动测试
testBacktest();
