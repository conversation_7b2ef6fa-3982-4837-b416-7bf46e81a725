/**
 * 币安Python交易服务 - 调用Python脚本执行币安交易
 */
const { spawn } = require('child_process');
const path = require('path');

// 币安Python脚本路径
const BINANCE_PYTHON_SCRIPT = path.join(__dirname, '..', 'python', 'binance_trader.py');

/**
 * 执行币安Python脚本
 * @param {Array} args 命令行参数
 * @param {number} [timeout=30000] 超时时间（毫秒）
 * @returns {Promise<Object>} 执行结果
 */
async function executeBinancePythonScript(args, timeout = 30000) {
  // 获取Python可执行文件路径
  const pythonPath = await getPythonPath();

  if (pythonPath) {
    console.log(`使用找到的Python路径执行币安脚本: ${pythonPath}`);
    return executeWithPythonPath(pythonPath, args, timeout);
  }

  return new Promise((resolve, reject) => {
    // 设置超时定时器
    const timeoutId = setTimeout(() => {
      console.error(`币安Python脚本执行超时(${timeout}ms)`);
      // 如果进程还在运行，尝试终止它
      if (pythonProcess && !pythonProcess.killed) {
        pythonProcess.kill();
      }
      if (python3Process && !python3Process.killed) {
        python3Process.kill();
      }
      reject({ error: `币安Python脚本执行超时(${timeout}ms)，请检查Python环境或网络连接` });
    }, timeout);

    // 清除超时定时器的函数
    const clearTimeoutTimer = () => {
      clearTimeout(timeoutId);
    };

    // 检查Python是否可用
    const pythonProcess = spawn('python', ['-V'], { windowsHide: true });

    pythonProcess.on('error', (error) => {
      console.error('Python不可用:', error);
      // 尝试使用python3
      const python3Process = spawn('python3', ['-V'], { windowsHide: true });

      python3Process.on('error', (error) => {
        console.error('Python3也不可用:', error);
        // 尝试使用py命令
        const pyProcess = spawn('py', ['-V'], { windowsHide: true });

        pyProcess.on('error', (error) => {
          console.error('py命令也不可用:', error);
          clearTimeoutTimer();
          reject({ error: 'Python环境不可用，请确保已安装Python' });
        });

        pyProcess.on('close', (code) => {
          if (code === 0) {
            // 使用py执行脚本
            executeWithPython('py', args, resolve, reject, clearTimeoutTimer);
          } else {
            clearTimeoutTimer();
            reject({ error: 'Python环境不可用，请确保已安装Python' });
          }
        });
      });

      python3Process.on('close', (code) => {
        if (code === 0) {
          // 使用python3执行脚本
          executeWithPython('python3', args, resolve, reject, clearTimeoutTimer);
        } else {
          // 尝试使用py命令
          const pyProcess = spawn('py', ['-V'], { windowsHide: true });

          pyProcess.on('error', (error) => {
            console.error('py命令也不可用:', error);
            clearTimeoutTimer();
            reject({ error: 'Python环境不可用，请确保已安装Python' });
          });

          pyProcess.on('close', (code) => {
            if (code === 0) {
              // 使用py执行脚本
              executeWithPython('py', args, resolve, reject, clearTimeoutTimer);
            } else {
              clearTimeoutTimer();
              reject({ error: 'Python环境不可用，请确保已安装Python' });
            }
          });
        }
      });
    });

    pythonProcess.on('close', (code) => {
      if (code === 0) {
        // 使用python执行脚本
        executeWithPython('python', args, resolve, reject, clearTimeoutTimer);
      } else {
        // 尝试使用python3
        const python3Process = spawn('python3', ['-V'], { windowsHide: true });

        python3Process.on('error', (error) => {
          console.error('Python3也不可用:', error);
          clearTimeoutTimer();
          reject({ error: 'Python环境不可用，请确保已安装Python' });
        });

        python3Process.on('close', (code) => {
          if (code === 0) {
            // 使用python3执行脚本
            executeWithPython('python3', args, resolve, reject, clearTimeoutTimer);
          } else {
            clearTimeoutTimer();
            reject({ error: 'Python环境不可用，请确保已安装Python' });
          }
        });
      }
    });
  });
}

/**
 * 获取Python可执行文件路径
 * @returns {Promise<string|null>} Python路径或null
 */
async function getPythonPath() {
  // 常见的Python路径
  const commonPaths = [
    'C:\\Python39\\python.exe',
    'C:\\Python38\\python.exe',
    'C:\\Python37\\python.exe',
    'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\python.exe',
    'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\python.exe',
    'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\python.exe',
    '/usr/bin/python3',
    '/usr/local/bin/python3',
    '/opt/homebrew/bin/python3'
  ];

  const fs = require('fs').promises;

  for (const pythonPath of commonPaths) {
    try {
      // 展开环境变量
      const expandedPath = pythonPath.replace('%USERNAME%', process.env.USERNAME || process.env.USER || '');
      await fs.access(expandedPath);
      console.log(`找到Python路径: ${expandedPath}`);
      return expandedPath;
    } catch (error) {
      // 文件不存在，继续尝试下一个
    }
  }

  return null;
}

/**
 * 使用指定的Python路径执行脚本
 * @param {string} pythonPath Python可执行文件路径
 * @param {Array} args 命令行参数
 * @param {number} timeout 超时时间（毫秒）
 * @returns {Promise<Object>} 执行结果
 */
function executeWithPythonPath(pythonPath, args, timeout) {
  return new Promise((resolve, reject) => {
    const fullArgs = [BINANCE_PYTHON_SCRIPT, ...args];
    console.log(`使用Python路径执行币安脚本: ${pythonPath} ${fullArgs.join(' ')}`);

    // 设置超时定时器
    const timeoutId = setTimeout(() => {
      console.error(`币安Python脚本执行超时(${timeout}ms)`);
      if (pythonProcess && !pythonProcess.killed) {
        pythonProcess.kill();
      }
      reject({ error: `币安Python脚本执行超时(${timeout}ms)，请检查Python环境或网络连接` });
    }, timeout);

    const pythonProcess = spawn(pythonPath, fullArgs, {
      windowsHide: true,
      env: { ...process.env, PYTHONIOENCODING: 'utf-8' }
    });
    let stdout = '';
    let stderr = '';

    pythonProcess.stdout.on('data', (data) => {
      stdout += data.toString('utf8');
    });

    pythonProcess.stderr.on('data', (data) => {
      stderr += data.toString('utf8');
    });

    pythonProcess.on('close', (code) => {
      clearTimeout(timeoutId);
      console.log(`币安Python脚本执行完成，退出码: ${code}`);

      if (code === 0) {
        try {
          // 尝试解析JSON输出
          const result = JSON.parse(stdout);
          resolve(result);
        } catch (error) {
          console.error('解析币安Python脚本输出失败:', error);
          console.error('Python输出:', stdout);
          resolve({ output: stdout });
        }
      } else {
        console.error('币安Python脚本执行失败:', stderr);
        reject({ error: stderr || '执行币安Python脚本失败' });
      }
    });

    pythonProcess.on('error', (error) => {
      clearTimeout(timeoutId);
      console.error('启动币安Python脚本失败:', error);
      reject({ error: `启动币安Python脚本失败: ${error.message}` });
    });
  });
}

/**
 * 使用指定的Python解释器执行脚本
 * @param {string} pythonCommand Python命令
 * @param {Array} args 命令行参数
 * @param {Function} resolve Promise解析函数
 * @param {Function} reject Promise拒绝函数
 * @param {Function} clearTimeout 清除超时定时器的函数
 */
function executeWithPython(pythonCommand, args, resolve, reject, clearTimeout) {
  const fullArgs = [BINANCE_PYTHON_SCRIPT, ...args];
  console.log(`执行币安Python脚本: ${pythonCommand} ${fullArgs.join(' ')}`);

  const pythonProcess = spawn(pythonCommand, fullArgs, {
    windowsHide: true,
    env: { ...process.env, PYTHONIOENCODING: 'utf-8' }
  });
  let stdout = '';
  let stderr = '';

  pythonProcess.stdout.on('data', (data) => {
    stdout += data.toString('utf8');
  });

  pythonProcess.stderr.on('data', (data) => {
    stderr += data.toString('utf8');
  });

  pythonProcess.on('close', (code) => {
    // 清除超时定时器
    if (clearTimeout) {
      clearTimeout();
    }

    console.log(`币安Python脚本执行完成，退出码: ${code}`);

    if (code === 0) {
      try {
        // 尝试解析JSON输出
        const result = JSON.parse(stdout);
        resolve(result);
      } catch (error) {
        console.error('解析币安Python脚本输出失败:', error);
        console.error('Python输出:', stdout);
        resolve({ output: stdout });
      }
    } else {
      console.error('币安Python脚本执行失败:', stderr);
      reject({ error: stderr || '执行币安Python脚本失败' });
    }
  });

  pythonProcess.on('error', (error) => {
    // 清除超时定时器
    if (clearTimeout) {
      clearTimeout();
    }

    console.error('启动币安Python脚本失败:', error);
    reject({ error: `启动币安Python脚本失败: ${error.message}` });
  });
}

/**
 * 检查Python环境
 * @returns {Promise<Object>} 检查结果
 */
async function checkEnvironment() {
  try {
    const result = await executeBinancePythonScript(['--version'], 5000);
    return { success: true, ...result };
  } catch (error) {
    return { success: false, error: error.error || error.message };
  }
}

/**
 * 获取币安账户余额
 * @param {string} userId 用户ID
 * @returns {Promise<Object>} 账户余额信息
 */
async function getBinanceAccountBalance(userId = 'default') {
  return executeBinancePythonScript(['balance', userId], 15000);
}

/**
 * 获取币安市场价格
 * @param {string} symbol 交易对，如 'BTCUSDT'
 * @returns {Promise<Object>} 市场价格信息
 */
async function getBinanceMarketPrice(symbol) {
  return executeBinancePythonScript(['price', symbol], 10000);
}

/**
 * 执行币安交易
 * @param {string} symbol 交易对，如 'BTCUSDT'
 * @param {string} side 交易方向，'BUY' 或 'SELL'
 * @param {number} amount 交易数量
 * @param {number} [price] 限价单价格，如果为undefined则为市价单
 * @returns {Promise<Object>} 交易结果
 */
async function executeBinanceTrade(symbol, side, amount, price) {
  const args = ['trade', symbol, side, amount.toString()];
  if (price !== undefined) {
    args.push(price.toString());
  }
  // 使用20秒超时
  return executeBinancePythonScript(args, 20000);
}

/**
 * 获取币安K线数据
 * @param {string} symbol 交易对，如 'BTCUSDT'
 * @param {string} timeframe 时间周期，如 '1h', '4h', '1d'
 * @param {number} limit 获取数量，默认100
 * @returns {Promise<Object>} K线数据
 */
async function getBinanceKlines(symbol, timeframe = '1h', limit = 100) {
  try {
    const args = ['klines', symbol, timeframe, limit.toString()];
    return await executeBinancePythonScript(args, 15000);
  } catch (error) {
    console.error('获取币安K线数据失败:', error);
    throw error;
  }
}

/**
 * 获取币安历史K线数据
 * @param {string} symbol 交易对
 * @param {string} timeframe 时间周期
 * @param {number} limit 获取数量
 * @param {number} startTime 开始时间戳（毫秒）
 * @param {number} endTime 结束时间戳（毫秒）
 * @returns {Promise<Object>} K线数据
 */
async function getBinanceHistoricalKlines(symbol, timeframe, limit, startTime, endTime) {
  try {
    const args = [
      'klines',
      symbol,
      timeframe,
      limit.toString(),
      startTime ? startTime.toString() : 'null',
      endTime ? endTime.toString() : 'null'
    ];

    console.log(`📊 调用Python脚本获取历史K线: ${symbol} ${timeframe} ${new Date(startTime)} 到 ${new Date(endTime)}`);
    return await executeBinancePythonScript(args, 30000); // 增加超时时间
  } catch (error) {
    console.error('获取币安历史K线数据失败:', error);
    throw error;
  }
}

/**
 * 获取币安历史订单
 * @param {string} userId 用户ID
 * @param {Object} options 查询选项
 * @returns {Promise<Object>} 历史订单数据
 */
async function getBinanceOrderHistory(userId, options = {}) {
  try {
    const { symbol, limit = 20, page = 1 } = options;
    const args = ['orders', userId];

    // 添加symbol参数，如果没有则传递undefined占位符
    args.push(symbol || 'undefined');
    args.push(limit.toString());
    args.push(page.toString());

    console.log('调用币安Python脚本获取历史订单，参数:', args);
    return await executeBinancePythonScript(args, 15000);
  } catch (error) {
    console.error('获取币安历史订单失败:', error);
    throw error;
  }
}

module.exports = {
  checkEnvironment,
  getBinanceAccountBalance,
  getBinanceMarketPrice,
  executeBinanceTrade,
  getBinanceKlines,
  getBinanceHistoricalKlines,
  getBinanceOrderHistory
};
