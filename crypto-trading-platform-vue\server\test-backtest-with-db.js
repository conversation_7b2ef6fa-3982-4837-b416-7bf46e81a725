/**
 * 测试回测功能使用数据库K线数据
 */

require('dotenv').config();
process.env.USE_MONGODB = 'true';

const mongoose = require('mongoose');
const backtestService = require('./services/backtestService');
const klineDataService = require('./services/klineDataService');

// 连接MongoDB数据库
async function connectDB() {
  try {
    const mongoURI = process.env.MONGODB_URI || 'mongodb://localhost:27017/crypto-trading-platform';
    await mongoose.connect(mongoURI);
    console.log('✅ MongoDB连接成功');
    return true;
  } catch (error) {
    console.error('❌ MongoDB连接失败:', error.message);
    return false;
  }
}

// 准备测试数据
async function prepareTestData() {
  console.log('\n📊 准备回测测试数据...');
  
  try {
    // 确保有足够的K线数据用于回测
    const now = Date.now();
    const oneDayAgo = now - 24 * 3600000;
    
    // 检查现有数据
    const existingData = await klineDataService.getKlineDataFromDB(
      'binance', 
      'BTCUSDT', 
      '1h', 
      oneDayAgo, 
      now
    );
    
    console.log(`📈 现有数据: ${existingData.length} 条`);
    
    if (existingData.length < 10) {
      console.log('📥 数据不足，获取更多数据...');
      
      // 从API获取更多数据并存储
      const moreData = await klineDataService.getKlineData(
        'binance',
        'BTCUSDT',
        '1h',
        oneDayAgo,
        now,
        24 // 24小时数据
      );
      
      console.log(`✅ 获取到 ${moreData.length} 条数据`);
      
      // 存储到数据库
      if (moreData.length > 0) {
        await klineDataService.storeKlineData('binance', 'BTCUSDT', '1h', moreData);
        console.log('💾 数据已存储到数据库');
      }
    }
    
    return true;
  } catch (error) {
    console.error('❌ 准备测试数据失败:', error);
    return false;
  }
}

// 测试回测功能
async function testBacktestWithDatabase() {
  console.log('\n🧪 测试回测功能使用数据库数据...');
  
  try {
    // 设置回测参数
    const backtestParams = {
      exchange: 'binance',
      strategyId: 'binance_futures_strategy1', // 趋势动量策略
      symbol: 'BTCUSDT',
      timeframe: '1h',
      startDate: new Date(Date.now() - 12 * 3600000).toISOString(), // 12小时前
      endDate: new Date().toISOString(), // 现在
      initialCapital: 10000,
      feeRate: 0.1, // 0.1%
      strategyParams: {
        rsiPeriod: 14,
        rsiOverbought: 70,
        rsiOversold: 30
      },
      userId: 'test-user'
    };
    
    console.log('📊 回测参数:');
    console.log(`  - 交易所: ${backtestParams.exchange}`);
    console.log(`  - 策略: ${backtestParams.strategyId}`);
    console.log(`  - 交易对: ${backtestParams.symbol}`);
    console.log(`  - 时间周期: ${backtestParams.timeframe}`);
    console.log(`  - 时间范围: ${backtestParams.startDate} 到 ${backtestParams.endDate}`);
    console.log(`  - 初始资金: $${backtestParams.initialCapital}`);
    
    // 首先创建测试用户的API密钥（模拟）
    const binanceService = require('./services/binanceService');
    binanceService.setUserApiKeys('test-user', {
      apiKey: 'test-api-key',
      secretKey: 'test-secret-key'
    });
    
    console.log('🚀 开始执行回测...');
    
    // 执行回测
    const result = await backtestService.runBacktest(backtestParams);
    
    if (result.success) {
      console.log('✅ 回测执行成功！');
      console.log('📊 回测结果:');
      
      const results = result.results;
      console.log(`  - 最终资金: $${results.finalCapital.toFixed(2)}`);
      console.log(`  - 总收益: $${results.totalProfit.toFixed(2)}`);
      console.log(`  - 收益率: ${results.totalReturn.toFixed(2)}%`);
      console.log(`  - 总交易次数: ${results.totalTrades}`);
      console.log(`  - 胜率: ${results.winRate.toFixed(2)}%`);
      console.log(`  - 最大回撤: ${results.maxDrawdown.toFixed(2)}%`);
      console.log(`  - 夏普比率: ${results.sharpeRatio.toFixed(4)}`);
      
      if (results.trades && results.trades.length > 0) {
        console.log(`📈 交易记录: ${results.trades.length} 笔交易`);
        console.log('  前3笔交易:');
        results.trades.slice(0, 3).forEach((trade, index) => {
          console.log(`    ${index + 1}. ${trade.type} at $${trade.price} - ${trade.side} - P&L: $${trade.pnl?.toFixed(2) || 'N/A'}`);
        });
      }
      
      return true;
    } else {
      console.log('❌ 回测执行失败:', result.error);
      return false;
    }
    
  } catch (error) {
    console.error('❌ 回测测试失败:', error);
    return false;
  }
}

// 测试数据库数据优先级
async function testDatabasePriority() {
  console.log('\n🔍 测试数据库数据优先级...');
  
  try {
    const now = Date.now();
    const sixHoursAgo = now - 6 * 3600000;
    
    // 直接测试getHistoricalData函数
    console.log('📊 测试历史数据获取（应优先使用数据库）...');
    
    // 模拟回测服务中的getHistoricalData调用
    const startDate = new Date(sixHoursAgo).toISOString();
    const endDate = new Date(now).toISOString();
    
    console.log(`⏰ 请求时间范围: ${startDate} 到 ${endDate}`);
    
    // 使用klineDataService直接测试
    const klines = await klineDataService.getKlineData(
      'binance',
      'BTCUSDT',
      '1h',
      sixHoursAgo,
      now,
      6,
      'test-user'
    );
    
    console.log(`✅ 获取到 ${klines.length} 条K线数据`);
    
    if (klines.length > 0) {
      console.log(`📊 数据来源验证:`);
      console.log(`  - 第一条: ${new Date(klines[0].timestamp)} - $${klines[0].close}`);
      console.log(`  - 最后一条: ${new Date(klines[klines.length - 1].timestamp)} - $${klines[klines.length - 1].close}`);
      
      // 检查数据是否来自数据库（通过查询数据库验证）
      const dbData = await klineDataService.getKlineDataFromDB(
        'binance',
        'BTCUSDT',
        '1h',
        sixHoursAgo,
        now
      );
      
      console.log(`💾 数据库中有 ${dbData.length} 条匹配数据`);
      
      if (dbData.length > 0) {
        console.log('✅ 确认数据优先从数据库获取');
      } else {
        console.log('⚠️ 数据主要来自API');
      }
    }
    
    return true;
  } catch (error) {
    console.error('❌ 数据库优先级测试失败:', error);
    return false;
  }
}

// 主测试函数
async function runBacktestTests() {
  console.log('🚀 开始回测功能数据库集成测试\n');
  console.log('=' .repeat(60));

  // 连接数据库
  const dbConnected = await connectDB();
  if (!dbConnected) {
    console.log('❌ 无法连接数据库，退出测试');
    return;
  }

  // 加载数据模型
  require('./models/KlineData');
  require('./models/Strategy');
  require('./models/User');

  let passedTests = 0;
  let totalTests = 0;

  // 运行测试
  const tests = [
    { name: '准备测试数据', func: prepareTestData },
    { name: '数据库优先级', func: testDatabasePriority },
    { name: '回测功能集成', func: testBacktestWithDatabase }
  ];

  for (const test of tests) {
    totalTests++;
    console.log(`\n🧪 运行测试: ${test.name}`);
    try {
      const success = await test.func();
      if (success) {
        passedTests++;
        console.log(`✅ ${test.name} 测试通过`);
      } else {
        console.log(`❌ ${test.name} 测试失败`);
      }
    } catch (error) {
      console.error(`❌ ${test.name} 测试异常:`, error.message);
    }
    
    // 测试间隔
    if (totalTests < tests.length) {
      console.log('⏳ 等待3秒...');
      await new Promise(resolve => setTimeout(resolve, 3000));
    }
  }

  // 测试总结
  console.log('\n' + '=' .repeat(60));
  console.log(`📊 回测集成测试总结: ${passedTests}/${totalTests} 项测试通过`);
  
  if (passedTests === totalTests) {
    console.log('🎉 回测功能数据库集成测试全部通过！');
    console.log('💡 现在回测功能可以使用数据库中的K线数据进行长时间回测了');
  } else {
    console.log('⚠️ 部分测试失败，请检查相关功能');
  }

  // 关闭数据库连接
  await mongoose.connection.close();
  console.log('📝 数据库连接已关闭');
}

// 运行测试
if (require.main === module) {
  runBacktestTests().catch(error => {
    console.error('测试运行失败:', error);
    process.exit(1);
  });
}

module.exports = { runBacktestTests };
