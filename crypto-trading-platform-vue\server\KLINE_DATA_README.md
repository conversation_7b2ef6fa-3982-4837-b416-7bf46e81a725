# K线数据预获取和存储功能

## 功能概述

本功能实现了K线数据的预获取和存储到MongoDB数据库中，以支持长时间的策略回测，克服了交易所API对单次K线获取的限制。

## 主要特性

### 1. 数据存储
- 支持币安(Binance)和OKX两个交易所的K线数据
- 支持多种时间周期：1m, 3m, 5m, 15m, 30m, 1h, 2h, 4h, 6h, 12h, 1d, 1w, 1M
- 自动去重，避免重复数据
- 高效的数据库索引，提升查询性能

### 2. 自动数据收集
- 定时自动从交易所获取最新K线数据
- 支持增量更新，只获取新的数据
- 可配置的收集间隔和交易对列表
- 错误重试机制

### 3. 智能数据获取
- 优先从数据库获取历史数据
- 数据不足时自动从API补充
- 支持长时间范围的回测（突破API限制）

## 文件结构

```
server/
├── models/
│   └── KlineData.js              # K线数据模型
├── services/
│   ├── klineDataService.js       # K线数据服务
│   ├── dataCollectionService.js  # 数据收集服务
│   └── backtestService.js        # 回测服务（已修改）
├── routes/
│   └── klineData.js              # K线数据API路由
└── test-kline-basic.js           # 基础功能测试
```

## 数据模型

### KlineData Schema
```javascript
{
  exchange: String,      // 交易所 ('binance' | 'okx')
  symbol: String,        // 交易对 (如 'BTCUSDT')
  timeframe: String,     // 时间周期 (如 '1h')
  timestamp: Number,     // 时间戳
  open: Number,          // 开盘价
  high: Number,          // 最高价
  low: Number,           // 最低价
  close: Number,         // 收盘价
  volume: Number,        // 成交量
  createdAt: Date,       // 创建时间
  updatedAt: Date        // 更新时间
}
```

## API接口

### 1. 获取K线数据
```
GET /api/kline-data/:exchange/:symbol/:timeframe
```

参数：
- `exchange`: 交易所 (binance/okx)
- `symbol`: 交易对 (如 BTCUSDT)
- `timeframe`: 时间周期 (如 1h)
- `startTime`: 开始时间戳 (可选)
- `endTime`: 结束时间戳 (可选)
- `limit`: 限制数量 (可选，默认1000)
- `userId`: 用户ID (可选，OKX需要)

### 2. 获取数据统计
```
GET /api/kline-data/stats
```

### 3. 手动触发数据收集
```
POST /api/kline-data/collect
```

### 4. 获取收集服务状态
```
GET /api/kline-data/collection-status
```

### 5. 启动/停止数据收集服务
```
POST /api/kline-data/start-collection
POST /api/kline-data/stop-collection
```

### 6. 存储K线数据
```
POST /api/kline-data/store
```

### 7. 清理过期数据
```
DELETE /api/kline-data/cleanup?daysToKeep=365
```

## 使用方法

### 1. 启动服务
服务器启动时会自动：
- 加载K线数据模型
- 启动数据收集服务
- 开始定时收集数据

### 2. 配置数据收集
默认配置：
```javascript
{
  symbols: ['BTCUSDT', 'ETHUSDT', 'BNBUSDT', ...], // 20个主要交易对
  timeframes: ['1h', '4h', '1d'],                   // 3个时间周期
  exchanges: ['binance', 'okx'],                    // 2个交易所
  batchSize: 500,                                   // 每次获取500条
  collectionInterval: 60,                           // 每60分钟收集一次
  autoCollection: true                              // 启用自动收集
}
```

### 3. 回测使用
回测服务已自动集成K线数据功能：
- 优先从数据库获取历史数据
- 支持更长时间范围的回测
- 数据不足时自动从API补充

## 测试

### 运行基础功能测试
```bash
node test-kline-basic.js
```

测试内容：
- K线数据模型功能
- 数据存储和查询
- 数据收集服务配置

## 配置说明

### 环境变量
- `MONGODB_URI`: MongoDB连接字符串
- `USE_MONGODB`: 设置为 'true' 启用MongoDB

### 数据收集配置
可通过API动态更新配置：
```javascript
PUT /api/kline-data/collection-config
{
  "symbols": ["BTCUSDT", "ETHUSDT"],
  "timeframes": ["1h", "4h"],
  "exchanges": ["binance"],
  "batchSize": 1000,
  "collectionInterval": 30,
  "autoCollection": true
}
```

## 性能优化

### 数据库索引
- 复合索引：exchange + symbol + timeframe + timestamp
- 时间范围查询索引：exchange + symbol + timeframe + timestamp(降序)

### 数据管理
- 自动去重机制
- 批量插入操作
- 定期清理过期数据

## 注意事项

1. **API限制**: 币安和OKX都有API调用频率限制，数据收集服务已考虑这些限制
2. **存储空间**: K线数据会占用较多存储空间，建议定期清理过期数据
3. **网络稳定性**: 数据收集依赖网络连接，建议在稳定的网络环境下运行
4. **API密钥**: OKX的数据收集需要用户API密钥，币安可使用公开API

## 故障排除

### 常见问题
1. **数据库连接失败**: 检查MongoDB服务和连接字符串
2. **API调用失败**: 检查网络连接和API密钥配置
3. **数据收集停止**: 查看服务日志，可能是API限制或网络问题

### 日志查看
服务会输出详细的日志信息，包括：
- 数据收集进度
- API调用状态
- 错误信息和重试情况
