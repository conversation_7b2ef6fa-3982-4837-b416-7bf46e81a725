/**
 * 策略回测服务 - 使用真实历史数据进行回测
 */

const binanceService = require('./binanceService');
const okxService = require('./okxService');
const binancePythonTradeService = require('./binancePythonTradeService');
const marketDataService = require('./marketDataService');
const klineDataService = require('./klineDataService');
const { binanceFuturesStrategies, binanceSpotStrategies } = require('./binanceStrategyDefinitionService');
const { futuresStrategies, spotStrategies } = require('./strategyDefinitionService');

/**
 * 运行回测
 * @param {Object} params 回测参数
 * @returns {Promise<Object>} 回测结果
 */
async function runBacktest(params) {
  try {
    const {
      exchange,
      strategyId,
      symbol,
      timeframe,
      startDate,
      endDate,
      initialCapital,
      feeRate,
      strategyParams,
      userId
    } = params;

    console.log('开始回测:', {
      exchange,
      strategyId,
      symbol,
      timeframe,
      startDate,
      endDate,
      initialCapital
    });

    // 回测功能使用公开K线数据，不需要API密钥验证
    console.log(`📊 回测将使用${exchange === 'binance' ? '币安' : 'OKX'}公开K线数据`);

    // 对于币安，K线数据完全公开，不需要API密钥
    // 对于OKX，如果用户配置了API密钥则使用，否则跳过
    let hasApiKeys = false;
    if (exchange === 'binance') {
      hasApiKeys = true; // 币安K线数据总是可用的
      console.log(`✅ 币安公开K线数据可用`);
    } else if (exchange === 'okx') {
      const apiKeys = okxService.getUserApiKeys(userId);
      hasApiKeys = apiKeys && apiKeys.apiKey && apiKeys.secretKey && apiKeys.passphrase;
      if (!hasApiKeys) {
        console.log(`⚠️ OKX API密钥未配置，回测功能需要API密钥`);
        throw new Error('OKX回测需要配置API密钥才能获取历史数据');
      }
    }

    // 获取策略定义
    const strategyDef = getStrategyDefinition(exchange, strategyId);
    if (!strategyDef) {
      throw new Error(`策略 ${strategyId} 未找到`);
    }

    // 获取历史K线数据
    const klineData = await getHistoricalData(exchange, symbol, timeframe, startDate, endDate, userId);
    if (!klineData || klineData.length === 0) {
      throw new Error('无法获取历史K线数据');
    }

    console.log(`获取到 ${klineData.length} 条K线数据`);

    // 检查数据覆盖范围
    const actualStartTime = new Date(klineData[0].timestamp);
    const actualEndTime = new Date(klineData[klineData.length - 1].timestamp);
    const requestedStartTime = new Date(startDate);
    const requestedEndTime = new Date(endDate);

    if (actualStartTime > requestedStartTime || actualEndTime < requestedEndTime) {
      console.log(`⚠️ 数据覆盖范围受限：请求 ${requestedStartTime.toISOString()} 到 ${requestedEndTime.toISOString()}`);
      console.log(`⚠️ 实际获取：${actualStartTime.toISOString()} 到 ${actualEndTime.toISOString()}`);
      console.log(`⚠️ 建议使用更大的时间周期（如1h或4h）进行长期回测`);
    }

    // 执行回测
    const backtestResults = await executeBacktest({
      strategyDef,
      klineData,
      initialCapital,
      feeRate: feeRate / 100, // 转换为小数
      strategyParams
    });

    return {
      success: true,
      results: backtestResults
    };

  } catch (error) {
    console.error('回测失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * 获取策略定义
 * @param {string} exchange 交易所
 * @param {string} strategyId 策略ID
 * @returns {Object|null} 策略定义
 */
function getStrategyDefinition(exchange, strategyId) {
  if (exchange === 'binance') {
    // 币安策略
    if (strategyId.includes('futures')) {
      return binanceFuturesStrategies[strategyId];
    } else if (strategyId.includes('spot')) {
      return binanceSpotStrategies[strategyId];
    }
  } else if (exchange === 'okx') {
    // OKX策略
    if (strategyId.includes('futures')) {
      return futuresStrategies[strategyId];
    } else if (strategyId.includes('spot')) {
      return spotStrategies[strategyId];
    }
  }
  return null;
}

/**
 * 获取历史数据（优先从数据库获取，不足时从API补充）
 * @param {string} exchange 交易所
 * @param {string} symbol 交易对
 * @param {string} timeframe 时间周期
 * @param {string} startDate 开始日期
 * @param {string} endDate 结束日期
 * @param {string} userId 用户ID
 * @returns {Promise<Array>} K线数据
 */
async function getHistoricalData(exchange, symbol, timeframe, startDate, endDate, userId) {
  try {
    const start = new Date(startDate).getTime();
    const end = new Date(endDate).getTime();

    // 计算需要获取的K线数量，但设置合理的限制
    const timeframeMs = getTimeframeMs(timeframe);
    let limit = Math.ceil((end - start) / timeframeMs);

    // 根据时间周期设置合理的限制，考虑API限制和实际需求
    const timeRangeHours = (end - start) / (1000 * 3600); // 时间范围（小时）

    if (timeframe === '1m') {
      // 1分钟数据：建议最多7天
      if (timeRangeHours > 168) { // 7天
        console.log(`⚠️ 1分钟K线回测时间范围过大（${timeRangeHours.toFixed(1)}小时），建议不超过7天（168小时）`);
        console.log(`💡 建议使用更大的时间周期（如5m、15m、1h）进行长期回测`);
      }
      limit = Math.min(limit, 10080); // 7天的1分钟数据
    } else if (timeframe === '5m') {
      // 5分钟数据：建议最多30天
      if (timeRangeHours > 720) { // 30天
        console.log(`⚠️ 5分钟K线回测时间范围过大（${timeRangeHours.toFixed(1)}小时），建议不超过30天`);
      }
      limit = Math.min(limit, 8640); // 30天的5分钟数据
    } else if (timeframe === '15m') {
      limit = Math.min(limit, 10000); // 15分钟最多10000条（约104天）
    } else if (timeframe === '1h') {
      limit = Math.min(limit, 8760); // 1小时最多8760条（1年）
    } else if (timeframe === '4h') {
      limit = Math.min(limit, 2190); // 4小时最多2190条（1年）
    } else if (timeframe === '1d') {
      limit = Math.min(limit, 365); // 1天最多365条（1年）
    } else {
      limit = Math.min(limit, 5000); // 其他时间周期最多5000条
    }

    console.log(`📊 时间范围: ${timeRangeHours.toFixed(1)}小时，限制数量: ${limit}`);

    console.log(`📊 获取历史数据: ${exchange}, ${symbol}, ${timeframe}, 时间范围: ${startDate} 到 ${endDate}, 预计数量: ${limit}`);

    // 使用新的K线数据服务，优先从数据库获取
    let klines;
    try {
      klines = await klineDataService.getKlineData(
        exchange,
        symbol,
        timeframe,
        start,
        end,
        limit,
        userId
      );
    } catch (apiError) {
      console.error(`❌ K线数据服务失败: ${apiError.message}`);

      // 如果是时间范围过大的问题，提供更具体的建议
      if (timeRangeHours > 8760) { // 超过1年
        throw new Error(`回测时间范围过大（${timeRangeHours.toFixed(1)}小时），请缩短时间范围或使用更大的时间周期。建议：1年内用1h，1个月内用15m，1周内用5m，1天内用1m。`);
      } else if (timeframe === '1m' && timeRangeHours > 168) {
        throw new Error(`1分钟K线回测时间范围过大（${timeRangeHours.toFixed(1)}小时），建议不超过7天或使用5m/15m时间周期。`);
      } else {
        throw new Error(`无法获取K线数据: ${apiError.message}。请检查网络连接、API密钥配置，或尝试缩短回测时间范围。`);
      }
    }

    if (!klines || klines.length === 0) {
      // 提供更详细的错误信息
      const suggestions = [];
      if (timeframe === '1m') {
        suggestions.push('尝试使用5m或15m时间周期');
      }
      if (timeRangeHours > 720) {
        suggestions.push('缩短回测时间范围');
      }
      suggestions.push('检查API密钥配置');
      suggestions.push('确认交易对名称正确');

      const suggestionText = suggestions.length > 0 ? ` 建议：${suggestions.join('，')}。` : '';
      throw new Error(`无法获取历史K线数据。${suggestionText}`);
    }

    // 按时间排序，确保数据顺序正确
    const sortedKlines = klines.sort((a, b) => a.timestamp - b.timestamp);

    console.log(`📊 ${exchange} K线数据获取完成: ${sortedKlines.length} 条记录`);
    if (sortedKlines.length > 0) {
      console.log(`📊 数据时间范围: ${new Date(sortedKlines[0].timestamp)} 到 ${new Date(sortedKlines[sortedKlines.length - 1].timestamp)}`);
    }

    return sortedKlines;

  } catch (error) {
    console.error('获取历史数据失败:', error);
    throw error;
  }
}

/**
 * 获取时间周期对应的毫秒数
 * @param {string} timeframe 时间周期
 * @returns {number} 毫秒数
 */
function getTimeframeMs(timeframe) {
  const timeframes = {
    '1m': 60 * 1000,
    '5m': 5 * 60 * 1000,
    '15m': 15 * 60 * 1000,
    '1h': 60 * 60 * 1000,
    '4h': 4 * 60 * 60 * 1000,
    '1d': 24 * 60 * 60 * 1000
  };
  return timeframes[timeframe] || 60 * 60 * 1000; // 默认1小时
}

/**
 * 执行回测逻辑
 * @param {Object} params 回测参数
 * @returns {Promise<Object>} 回测结果
 */
async function executeBacktest(params) {
  const { strategyDef, klineData, initialCapital, feeRate, strategyParams } = params;

  // 回测状态
  let capital = initialCapital;
  let position = 0; // 持仓数量
  let positionValue = 0; // 持仓价值
  let totalTrades = 0;
  let winningTrades = 0;
  let losingTrades = 0;
  let totalProfit = 0;
  let maxCapital = initialCapital;
  let maxDrawdown = 0;
  let trades = [];

  // 多多益善策略专用变量
  let isMultiAddStrategy = false;
  let entryPrice = 0; // 首次开仓价格
  let addPositionCount = 0; // 加仓次数
  let totalInvestment = 0; // 总投入金额

  // 检查是否是多多益善策略
  if (strategyDef.entryConditions &&
      (strategyDef.entryConditions.immediate === true || strategyDef.entryConditions.immediate?.enabled === true) &&
      strategyDef.addPositionConditions && strategyDef.addPositionConditions.priceDropTrigger) {
    isMultiAddStrategy = true;
    console.log('检测到多多益善策略，启用特殊回测逻辑');
    console.log('策略定义:', JSON.stringify(strategyDef, null, 2));
  }

  console.log('开始执行回测逻辑...');

  // 遍历K线数据执行策略
  for (let i = 1; i < klineData.length; i++) {
    const currentCandle = klineData[i];
    const prevCandle = klineData[i - 1];
    const price = currentCandle.close;

    if (isMultiAddStrategy) {
      // 多多益善策略的特殊逻辑
      if (position === 0 && capital > 100) {
        // 立即开仓（只要没有持仓就开仓）
        const tradeAmount = Math.min(capital * 0.15, 2000); // 使用15%的资金或最多2000，控制单次投入
        const quantity = tradeAmount / price;
        const fee = tradeAmount * feeRate;

        position = quantity;
        positionValue = tradeAmount;
        totalInvestment = tradeAmount;
        capital -= (tradeAmount + fee);
        entryPrice = price;
        totalTrades++;

        trades.push({
          type: 'buy',
          price: price,
          quantity: quantity,
          timestamp: currentCandle.timestamp,
          fee: fee,
          addPosition: false
        });

        console.log(`多多益善开仓: 价格=${price}, 数量=${quantity.toFixed(6)}, 投入=${tradeAmount.toFixed(2)}, 剩余资金=${capital.toFixed(2)}`);
      } else if (position > 0) {
        // 检查加仓条件：价格每下跌1%加仓
        const dropPercent = ((entryPrice - price) / entryPrice) * 100;
        const shouldAddPositions = Math.floor(dropPercent / 1); // 每下跌1%

        if (shouldAddPositions > addPositionCount && capital > 50 && shouldAddPositions <= 10) {
          // 需要加仓（限制最多10次加仓）
          const addAmount = Math.min(capital * 0.15, totalInvestment * 0.8); // 每次加仓金额，不超过初始投入的80%
          const addQuantity = addAmount / price;
          const fee = addAmount * feeRate;

          position += addQuantity;
          positionValue += addAmount;
          totalInvestment += addAmount;
          capital -= (addAmount + fee);
          addPositionCount = shouldAddPositions;
          totalTrades++; // 每次加仓都算一笔交易

          trades.push({
            type: 'buy',
            price: price,
            quantity: addQuantity,
            timestamp: currentCandle.timestamp,
            fee: fee,
            addPosition: true,
            addPositionCount: addPositionCount
          });

          console.log(`多多益善加仓${addPositionCount}: 价格=${price}, 下跌=${dropPercent.toFixed(2)}%, 加仓=${addAmount.toFixed(2)}, 总投入=${totalInvestment.toFixed(2)}`);
        }

        // 检查出场条件：整体盈利5%
        const currentValue = position * price;
        const totalProfitPercent = ((currentValue - totalInvestment) / totalInvestment) * 100;

        if (totalProfitPercent >= 5) {
          // 全仓卖出
          const sellValue = position * price;
          const fee = sellValue * feeRate;
          const profit = sellValue - totalInvestment - fee;

          capital += (sellValue - fee);
          totalProfit += profit;
          totalTrades++; // 卖出也算一笔交易

          if (profit > 0) {
            winningTrades++;
          } else {
            losingTrades++;
          }

          trades.push({
            type: 'sell',
            price: price,
            quantity: position,
            timestamp: currentCandle.timestamp,
            fee: fee,
            profit: profit,
            totalInvestment: totalInvestment,
            profitPercent: totalProfitPercent
          });

          console.log(`多多益善全仓卖出: 价格=${price}, 盈利=${profit.toFixed(2)}, 盈利率=${totalProfitPercent.toFixed(2)}%`);

          // 重置状态，准备下一轮开仓
          position = 0;
          positionValue = 0;
          entryPrice = 0;
          addPositionCount = 0;
          totalInvestment = 0;

          // 缩短等待时间，增加交易频率（只跳过2根K线，即2分钟）
          i += 2;
        }
      }
    } else {
      // 普通策略逻辑
      if (position === 0) {
        const shouldEnter = checkEntryConditions(strategyDef, klineData, i, strategyParams);
        if (shouldEnter) {
          // 开仓
          const tradeAmount = capital * 0.95; // 使用95%的资金
          const quantity = tradeAmount / price;
          const fee = tradeAmount * feeRate;

          position = quantity;
          positionValue = tradeAmount;
          capital -= (tradeAmount + fee);
          totalTrades++;

          trades.push({
            type: 'buy',
            price: price,
            quantity: quantity,
            timestamp: currentCandle.timestamp,
            fee: fee
          });

          console.log(`开仓: 价格=${price}, 数量=${quantity.toFixed(6)}, 手续费=${fee.toFixed(2)}`);
        }
      } else {
        // 检查出场条件
        const shouldExit = checkExitConditions(strategyDef, klineData, i, strategyParams, positionValue, price * position);
        if (shouldExit) {
          // 平仓
          const sellValue = position * price;
          const fee = sellValue * feeRate;
          const profit = sellValue - positionValue - fee;

          capital += (sellValue - fee);
          totalProfit += profit;

          if (profit > 0) {
            winningTrades++;
          } else {
            losingTrades++;
          }

          trades.push({
            type: 'sell',
            price: price,
            quantity: position,
            timestamp: currentCandle.timestamp,
            fee: fee,
            profit: profit
          });

          console.log(`平仓: 价格=${price}, 盈亏=${profit.toFixed(2)}, 总资金=${capital.toFixed(2)}`);

          position = 0;
          positionValue = 0;
        }
      }
    }

    // 更新最大资金和最大回撤
    const currentValue = capital + (position > 0 ? position * price : 0);
    if (currentValue > maxCapital) {
      maxCapital = currentValue;
    }
    const drawdown = (maxCapital - currentValue) / maxCapital * 100;
    if (drawdown > maxDrawdown) {
      maxDrawdown = drawdown;
    }
  }

  // 如果还有持仓，按最后价格平仓
  if (position > 0) {
    const lastPrice = klineData[klineData.length - 1].close;
    const sellValue = position * lastPrice;
    const fee = sellValue * feeRate;

    let profit;
    if (isMultiAddStrategy) {
      // 多多益善策略：计算相对于总投入的盈亏
      profit = sellValue - totalInvestment - fee;
      console.log(`多多益善强制平仓: 价格=${lastPrice}, 总投入=${totalInvestment.toFixed(2)}, 卖出=${sellValue.toFixed(2)}, 盈亏=${profit.toFixed(2)}`);
    } else {
      // 普通策略：计算相对于持仓价值的盈亏
      profit = sellValue - positionValue - fee;
      console.log(`强制平仓: 价格=${lastPrice}, 盈亏=${profit.toFixed(2)}, 总资金=${capital.toFixed(2)}`);
    }

    capital += (sellValue - fee);
    totalProfit += profit;
    totalTrades++; // 强制平仓也算一笔交易

    if (profit > 0) {
      winningTrades++;
    } else {
      losingTrades++;
    }

    trades.push({
      type: 'sell',
      price: lastPrice,
      quantity: position,
      timestamp: klineData[klineData.length - 1].timestamp,
      fee: fee,
      profit: profit,
      forcedExit: true,
      totalInvestment: isMultiAddStrategy ? totalInvestment : positionValue
    });
  }

  // 计算回测指标
  const finalValue = capital + (position > 0 ? position * klineData[klineData.length - 1].close : 0);
  const totalReturn = ((finalValue - initialCapital) / initialCapital) * 100;
  const winRate = totalTrades > 0 ? (winningTrades / totalTrades) * 100 : 0;

  // 修正平均盈利和亏损的计算
  const profitTrades = trades.filter(t => t.profit && t.profit > 0);
  const lossTrades = trades.filter(t => t.profit && t.profit < 0);

  const avgWin = profitTrades.length > 0 ?
    (profitTrades.reduce((sum, t) => sum + t.profit, 0) / profitTrades.length) / initialCapital * 100 : 0;
  const avgLoss = lossTrades.length > 0 ?
    (lossTrades.reduce((sum, t) => sum + t.profit, 0) / lossTrades.length) / initialCapital * 100 : 0;

  const profitLossRatio = avgLoss !== 0 ? Math.abs(avgWin / avgLoss) : 0;
  const sharpeRatio = calculateSharpeRatio(trades, initialCapital);

  // 更新交易统计
  const actualWinningTrades = profitTrades.length;
  const actualLosingTrades = lossTrades.length;

  console.log('回测完成:', {
    totalReturn: totalReturn.toFixed(2),
    winRate: winRate.toFixed(1),
    totalTrades,
    winningTrades: actualWinningTrades,
    losingTrades: actualLosingTrades,
    maxDrawdown: maxDrawdown.toFixed(2)
  });

  return {
    totalReturn: parseFloat(totalReturn.toFixed(2)),
    winRate: parseFloat(winRate.toFixed(1)),
    sharpeRatio: parseFloat(sharpeRatio.toFixed(2)),
    maxDrawdown: parseFloat((-maxDrawdown).toFixed(2)),
    totalTrades,
    winningTrades: actualWinningTrades,
    losingTrades: actualLosingTrades,
    avgWin: parseFloat(avgWin.toFixed(2)),
    avgLoss: parseFloat(Math.abs(avgLoss).toFixed(2)), // 显示为正数
    profitLossRatio: parseFloat(profitLossRatio.toFixed(2)),
    finalCapital: parseFloat(finalValue.toFixed(2)),
    totalProfit: parseFloat(totalProfit.toFixed(2)),
    trades: trades.slice(-10) // 只返回最后10笔交易
  };
}

/**
 * 检查入场条件
 * @param {Object} strategyDef 策略定义
 * @param {Array} klineData K线数据
 * @param {number} index 当前索引
 * @param {Object} strategyParams 策略参数
 * @returns {boolean} 是否满足入场条件
 */
function checkEntryConditions(strategyDef, klineData, index, strategyParams) {
  try {
    const entryConditions = strategyDef.entryConditions;

    // 立即开仓策略（如多多益善）
    if (entryConditions.immediate === true || entryConditions.immediate?.enabled === true) {
      return index === 1; // 只在第一根K线开仓
    }

    // RSI趋势策略
    if (entryConditions.rsiTrendDown) {
      const rsi = calculateRSI(klineData, index, entryConditions.rsiTrendDown.period);
      return rsi < entryConditions.rsiTrendDown.threshold;
    }

    if (entryConditions.rsiTrendUp) {
      const rsi = calculateRSI(klineData, index, entryConditions.rsiTrendUp.period);
      return rsi > entryConditions.rsiTrendUp.threshold;
    }

    // RSI超卖策略
    if (entryConditions.rsiOversold) {
      const rsi = calculateRSI(klineData, index, entryConditions.rsiOversold.period);
      return rsi < entryConditions.rsiOversold.threshold;
    }

    // 震荡区间套利策略（RSI + 布林线）
    if (entryConditions.rsiOversoldOverbought && entryConditions.bollingerBandTouch) {
      const rsi = calculateRSI(klineData, index, entryConditions.rsiOversoldOverbought.period || 14);
      const bollinger = calculateBollingerBands(klineData, index,
        entryConditions.bollingerBandTouch.period || 20,
        entryConditions.bollingerBandTouch.stdDev || 2
      );
      const price = klineData[index].close;

      // 降低RSI阈值，增加交易机会
      const oversoldThreshold = entryConditions.rsiOversoldOverbought.oversoldThreshold * 1.2; // 从30提高到36
      const overboughtThreshold = entryConditions.rsiOversoldOverbought.overboughtThreshold * 0.9; // 从70降低到63

      // 做多条件：RSI超卖 OR 价格接近布林线下轨
      const longCondition = rsi < oversoldThreshold || price < bollinger.lower * 1.02; // 价格在下轨2%范围内

      // 做空条件：RSI超买 OR 价格接近布林线上轨
      const shortCondition = rsi > overboughtThreshold || price > bollinger.upper * 0.98; // 价格在上轨2%范围内

      return longCondition || shortCondition;
    }

    // MA交叉策略（简化版本，不要求成交量条件）
    if (entryConditions.maCross) {
      const fastMA = calculateMA(klineData, index, entryConditions.maCross.fastPeriod);
      const slowMA = calculateMA(klineData, index, entryConditions.maCross.slowPeriod);
      const prevFastMA = calculateMA(klineData, index - 1, entryConditions.maCross.fastPeriod);
      const prevSlowMA = calculateMA(klineData, index - 1, entryConditions.maCross.slowPeriod);

      if (entryConditions.maCross.type === 'crossUp') {
        // 简化条件：只要快线在慢线上方且之前在下方就开仓
        const crossUp = fastMA > slowMA && prevFastMA <= prevSlowMA;

        // 如果有成交量条件，简化检查（降低要求）
        if (entryConditions.volumeIncrease && crossUp) {
          const currentVolume = klineData[index].volume || 1000000;
          const avgVolume = calculateAverageVolume(klineData, index, entryConditions.volumeIncrease.days || 5);
          const volumeOk = currentVolume > avgVolume * (entryConditions.volumeIncrease.ratio * 0.8 || 1.2); // 降低成交量要求
          return volumeOk;
        }

        return crossUp;
      }

      if (entryConditions.maCross.type === 'crossDown') {
        return fastMA < slowMA && prevFastMA >= prevSlowMA;
      }
    }

    // 如果没有明确的入场条件，使用简单的趋势判断
    if (!entryConditions.maCross && !entryConditions.rsiTrendDown && !entryConditions.rsiTrendUp &&
        !entryConditions.rsiOversold && !entryConditions.rsiOversoldOverbought) {
      // 简单的价格趋势判断
      if (index >= 5) {
        const recentPrices = klineData.slice(index - 4, index + 1).map(k => k.close);
        const isUptrend = recentPrices[4] > recentPrices[0] && recentPrices[3] > recentPrices[1];
        return isUptrend && Math.random() > 0.7; // 30%概率开仓，增加交易频率
      }
    }

    // 为震荡策略添加额外的交易机会
    if (entryConditions.rsiOversoldOverbought) {
      // 每20根K线检查一次RSI，增加交易频率
      if (index % 20 === 0 && index >= 20) {
        const rsi = calculateRSI(klineData, index, 14);
        const price = klineData[index].close;
        const prevPrice = klineData[index - 1].close;

        // 价格波动较大时增加交易机会
        const priceChange = Math.abs((price - prevPrice) / prevPrice) * 100;
        if (priceChange > 1) { // 价格变动超过1%
          return rsi < 45 || rsi > 55; // 更宽松的RSI条件
        }
      }
    }

    // 默认不满足条件
    return false;
  } catch (error) {
    console.error('检查入场条件失败:', error);
    return false;
  }
}

/**
 * 检查出场条件
 * @param {Object} strategyDef 策略定义
 * @param {Array} klineData K线数据
 * @param {number} index 当前索引
 * @param {Object} strategyParams 策略参数
 * @param {number} entryValue 入场价值
 * @param {number} currentValue 当前价值
 * @returns {boolean} 是否满足出场条件
 */
function checkExitConditions(strategyDef, klineData, index, strategyParams, entryValue, currentValue) {
  try {
    const exitConditions = strategyDef.exitConditions;
    const profitPercent = ((currentValue - entryValue) / entryValue) * 100;

    // 简单的止盈止损逻辑
    const stopLoss = -5; // 5%止损
    const takeProfit = 8; // 8%止盈

    // 基本止盈止损
    if (profitPercent <= stopLoss || profitPercent >= takeProfit) {
      return true;
    }

    // 总盈利目标
    if (exitConditions.totalProfit) {
      return profitPercent >= exitConditions.totalProfit.percentage;
    }

    // RSI趋势反转
    if (exitConditions.rsiTrendReverse) {
      const rsi = calculateRSI(klineData, index, exitConditions.rsiTrendReverse.period);
      return rsi > exitConditions.rsiTrendReverse.threshold;
    }

    // RSI高位出场
    if (exitConditions.rsiHighExit) {
      const rsi = calculateRSI(klineData, index, exitConditions.rsiHighExit.period);
      return rsi > exitConditions.rsiHighExit.threshold;
    }

    // 震荡策略出场条件（RSI反转或布林线中轨）
    if (exitConditions.rsiReverse || exitConditions.bollingerMiddle) {
      const rsi = calculateRSI(klineData, index, 14);
      const bollinger = calculateBollingerBands(klineData, index, 20, 2);
      const price = klineData[index].close;

      // RSI反转出场（降低阈值，更容易触发）
      const rsiReverseExit = rsi > 60 || rsi < 40; // 原来是70和30，现在更宽松

      // 价格回归布林线中轨附近
      const middleLineExit = Math.abs(price - bollinger.middle) / bollinger.middle < 0.01; // 价格在中轨1%范围内

      // 盈利超过3%就出场
      const quickProfitExit = profitPercent > 3;

      return rsiReverseExit || middleLineExit || quickProfitExit;
    }

    // RSI和布林线组合出场
    if (exitConditions.rsiAndBollingerExit) {
      const rsi = calculateRSI(klineData, index, exitConditions.rsiAndBollingerExit.rsiCondition.period);
      const bollinger = calculateBollingerBands(klineData, index,
        exitConditions.rsiAndBollingerExit.bollingerCondition.period,
        exitConditions.rsiAndBollingerExit.bollingerCondition.stdDev
      );
      const price = klineData[index].close;

      return rsi > exitConditions.rsiAndBollingerExit.rsiCondition.threshold &&
             price > bollinger.upper;
    }

    // MA交叉出场
    if (exitConditions.maCross) {
      const fastMA = calculateMA(klineData, index, exitConditions.maCross.fastPeriod);
      const slowMA = calculateMA(klineData, index, exitConditions.maCross.slowPeriod);
      const prevFastMA = calculateMA(klineData, index - 1, exitConditions.maCross.fastPeriod);
      const prevSlowMA = calculateMA(klineData, index - 1, exitConditions.maCross.slowPeriod);

      if (exitConditions.maCross.type === 'crossDown') {
        return fastMA < slowMA && prevFastMA >= prevSlowMA;
      }
    }

    // 持仓时间过长强制出场（防止长期持仓）
    if (index % 50 === 0) { // 每50根K线检查一次
      return Math.random() > 0.8; // 20%概率强制出场
    }

    // 默认不满足条件
    return false;
  } catch (error) {
    console.error('检查出场条件失败:', error);
    return false;
  }
}

/**
 * 计算RSI指标
 * @param {Array} klineData K线数据
 * @param {number} index 当前索引
 * @param {number} period 周期
 * @returns {number} RSI值
 */
function calculateRSI(klineData, index, period = 14) {
  if (index < period) return 50; // 数据不足时返回中性值

  let gains = 0;
  let losses = 0;

  for (let i = index - period + 1; i <= index; i++) {
    const change = klineData[i].close - klineData[i - 1].close;
    if (change > 0) {
      gains += change;
    } else {
      losses += Math.abs(change);
    }
  }

  const avgGain = gains / period;
  const avgLoss = losses / period;

  if (avgLoss === 0) return 100;

  const rs = avgGain / avgLoss;
  const rsi = 100 - (100 / (1 + rs));

  return rsi;
}

/**
 * 计算移动平均线
 * @param {Array} klineData K线数据
 * @param {number} index 当前索引
 * @param {number} period 周期
 * @returns {number} MA值
 */
function calculateMA(klineData, index, period) {
  if (index < period - 1) return klineData[index].close;

  let sum = 0;
  for (let i = index - period + 1; i <= index; i++) {
    sum += klineData[i].close;
  }

  return sum / period;
}

/**
 * 计算布林线
 * @param {Array} klineData K线数据
 * @param {number} index 当前索引
 * @param {number} period 周期
 * @param {number} stdDev 标准差倍数
 * @returns {Object} 布林线值
 */
function calculateBollingerBands(klineData, index, period = 20, stdDev = 2) {
  if (index < period - 1) {
    const price = klineData[index].close;
    return { upper: price, middle: price, lower: price };
  }

  const ma = calculateMA(klineData, index, period);

  let variance = 0;
  for (let i = index - period + 1; i <= index; i++) {
    variance += Math.pow(klineData[i].close - ma, 2);
  }

  const standardDeviation = Math.sqrt(variance / period);

  return {
    upper: ma + (standardDeviation * stdDev),
    middle: ma,
    lower: ma - (standardDeviation * stdDev)
  };
}

/**
 * 计算平均成交量
 * @param {Array} klineData K线数据
 * @param {number} index 当前索引
 * @param {number} days 天数
 * @returns {number} 平均成交量
 */
function calculateAverageVolume(klineData, index, days = 5) {
  if (index < days) return 1000000; // 默认值

  let totalVolume = 0;
  for (let i = index - days + 1; i <= index; i++) {
    totalVolume += klineData[i].volume || 1000000;
  }

  return totalVolume / days;
}

/**
 * 计算夏普比率
 * @param {Array} trades 交易记录
 * @param {number} initialCapital 初始资金
 * @returns {number} 夏普比率
 */
function calculateSharpeRatio(trades, initialCapital) {
  if (trades.length === 0) return 0;

  const returns = trades
    .filter(trade => trade.profit !== undefined)
    .map(trade => trade.profit / initialCapital);

  if (returns.length === 0) return 0;

  const avgReturn = returns.reduce((sum, ret) => sum + ret, 0) / returns.length;
  const variance = returns.reduce((sum, ret) => sum + Math.pow(ret - avgReturn, 2), 0) / returns.length;
  const stdDev = Math.sqrt(variance);

  return stdDev === 0 ? 0 : avgReturn / stdDev;
}

module.exports = {
  runBacktest
};
