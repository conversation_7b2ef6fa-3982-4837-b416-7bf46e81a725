/**
 * 手动数据收集功能测试
 */

require('dotenv').config();
process.env.USE_MONGODB = 'true';

const mongoose = require('mongoose');
const klineDataService = require('./services/klineDataService');
const dataCollectionService = require('./services/dataCollectionService');

// 连接MongoDB数据库
async function connectDB() {
  try {
    const mongoURI = process.env.MONGODB_URI || 'mongodb://localhost:27017/crypto-trading-platform';
    await mongoose.connect(mongoURI);
    console.log('✅ MongoDB连接成功');
    return true;
  } catch (error) {
    console.error('❌ MongoDB连接失败:', error.message);
    return false;
  }
}

// 测试手动数据收集
async function testManualCollection() {
  console.log('\n🔄 测试手动数据收集功能...');
  
  try {
    // 配置数据收集服务（关闭自动收集，仅手动测试）
    const testConfig = {
      symbols: ['BTCUSDT'],
      timeframes: ['1h'],
      exchanges: ['binance'],
      batchSize: 10, // 小批量测试
      autoCollection: false
    };

    dataCollectionService.updateConfig(testConfig);
    console.log('✅ 测试配置已设置');

    // 手动收集单个交易对数据
    console.log('📊 开始收集 BTCUSDT 1小时K线数据...');
    const result = await dataCollectionService.manualCollect('binance', 'BTCUSDT', '1h');
    
    console.log('✅ 手动收集结果:');
    console.log(`  - 收集数量: ${result.collected} 条`);
    console.log(`  - 错误数量: ${result.errors} 个`);

    if (result.collected > 0) {
      console.log('🎉 手动数据收集成功！');
      
      // 验证数据是否已存储到数据库
      const dbData = await klineDataService.getKlineDataFromDB('binance', 'BTCUSDT', '1h');
      console.log(`📊 数据库中现有 ${dbData.length} 条 BTCUSDT 1h 数据`);
      
      if (dbData.length > 0) {
        const latest = dbData[dbData.length - 1];
        console.log(`📈 最新数据: ${new Date(latest.timestamp)} - 收盘价: ${latest.close}`);
      }
      
      return true;
    } else {
      console.log('⚠️ 没有收集到新数据（可能数据已存在）');
      return true; // 这也算正常情况
    }

  } catch (error) {
    console.error('❌ 手动数据收集测试失败:', error);
    return false;
  }
}

// 测试数据查询功能
async function testDataQuery() {
  console.log('\n🔍 测试数据查询功能...');
  
  try {
    const now = Date.now();
    const oneHourAgo = now - 3600000;
    const oneDayAgo = now - 24 * 3600000;

    // 查询最近24小时的数据
    const recentData = await klineDataService.getKlineDataFromDB(
      'binance', 
      'BTCUSDT', 
      '1h', 
      oneDayAgo, 
      now
    );

    console.log(`✅ 查询到最近24小时的数据: ${recentData.length} 条`);

    if (recentData.length > 0) {
      console.log(`📊 时间范围: ${new Date(recentData[0].timestamp)} 到 ${new Date(recentData[recentData.length - 1].timestamp)}`);
      console.log(`💰 价格范围: ${Math.min(...recentData.map(d => d.low))} - ${Math.max(...recentData.map(d => d.high))}`);
    }

    // 测试获取最新数据
    const latestTimestamp = await klineDataService.getLatestTimestamp('binance', 'BTCUSDT', '1h');
    if (latestTimestamp) {
      console.log(`⏰ 最新数据时间: ${new Date(latestTimestamp)}`);
    }

    return true;
  } catch (error) {
    console.error('❌ 数据查询测试失败:', error);
    return false;
  }
}

// 测试数据统计功能
async function testDataStatistics() {
  console.log('\n📊 测试数据统计功能...');
  
  try {
    const stats = await klineDataService.getDataStats();
    
    console.log('✅ 数据库统计信息:');
    if (stats.length === 0) {
      console.log('  - 暂无数据');
    } else {
      stats.forEach(stat => {
        const dateRange = `${new Date(stat.minTimestamp).toLocaleDateString()} - ${new Date(stat.maxTimestamp).toLocaleDateString()}`;
        console.log(`  - ${stat._id.exchange} ${stat._id.symbol} ${stat._id.timeframe}: ${stat.count} 条记录 (${dateRange})`);
      });
    }

    return true;
  } catch (error) {
    console.error('❌ 数据统计测试失败:', error);
    return false;
  }
}

// 测试智能数据获取（数据库+API）
async function testSmartDataRetrieval() {
  console.log('\n🧠 测试智能数据获取功能...');
  
  try {
    const now = Date.now();
    const twoDaysAgo = now - 2 * 24 * 3600000;

    // 请求较大范围的数据，测试数据库+API组合获取
    console.log('📊 请求最近48小时的数据（测试智能获取）...');
    const smartData = await klineDataService.getKlineData(
      'binance',
      'BTCUSDT',
      '1h',
      twoDaysAgo,
      now,
      48 // 48小时的数据
    );

    console.log(`✅ 智能获取结果: ${smartData.length} 条数据`);
    
    if (smartData.length > 0) {
      console.log(`📊 数据完整性: ${smartData.length}/48 条`);
      console.log(`⏰ 时间范围: ${new Date(smartData[0].timestamp)} 到 ${new Date(smartData[smartData.length - 1].timestamp)}`);
      
      // 检查数据连续性
      let gaps = 0;
      for (let i = 1; i < smartData.length; i++) {
        const timeDiff = smartData[i].timestamp - smartData[i-1].timestamp;
        if (timeDiff > 3600000 * 1.5) { // 超过1.5小时认为有间隙
          gaps++;
        }
      }
      console.log(`🔗 数据连续性: ${gaps} 个时间间隙`);
    }

    return true;
  } catch (error) {
    console.error('❌ 智能数据获取测试失败:', error);
    return false;
  }
}

// 主测试函数
async function runManualCollectionTests() {
  console.log('🚀 开始手动数据收集功能测试\n');
  console.log('=' .repeat(60));

  // 连接数据库
  const dbConnected = await connectDB();
  if (!dbConnected) {
    console.log('❌ 无法连接数据库，退出测试');
    return;
  }

  // 加载数据模型
  require('./models/KlineData');

  let passedTests = 0;
  let totalTests = 0;

  // 运行测试
  const tests = [
    { name: '手动数据收集', func: testManualCollection },
    { name: '数据查询', func: testDataQuery },
    { name: '数据统计', func: testDataStatistics },
    { name: '智能数据获取', func: testSmartDataRetrieval }
  ];

  for (const test of tests) {
    totalTests++;
    console.log(`\n🧪 运行测试: ${test.name}`);
    try {
      const success = await test.func();
      if (success) {
        passedTests++;
        console.log(`✅ ${test.name} 测试通过`);
      } else {
        console.log(`❌ ${test.name} 测试失败`);
      }
    } catch (error) {
      console.error(`❌ ${test.name} 测试异常:`, error.message);
    }
    
    // 测试间隔，避免API限制
    if (totalTests < tests.length) {
      console.log('⏳ 等待2秒避免API限制...');
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
  }

  // 测试总结
  console.log('\n' + '=' .repeat(60));
  console.log(`📊 手动收集测试总结: ${passedTests}/${totalTests} 项测试通过`);
  
  if (passedTests === totalTests) {
    console.log('🎉 所有手动收集功能测试通过！');
    console.log('💡 提示: 数据已存储到数据库，可用于回测功能');
  } else {
    console.log('⚠️ 部分测试失败，请检查相关功能');
  }

  // 关闭数据库连接
  await mongoose.connection.close();
  console.log('📝 数据库连接已关闭');
}

// 运行测试
if (require.main === module) {
  runManualCollectionTests().catch(error => {
    console.error('测试运行失败:', error);
    process.exit(1);
  });
}

module.exports = { runManualCollectionTests };
