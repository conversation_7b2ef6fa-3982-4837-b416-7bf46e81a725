const schedule = require('node-schedule');
const klineDataService = require('./klineDataService');

/**
 * 数据收集服务
 * 负责定期从交易所获取K线数据并存储到数据库
 */
class DataCollectionService {
  constructor() {
    this.jobs = new Map(); // 存储定时任务
    this.isRunning = false;
    
    // 默认配置
    this.config = {
      // 支持的交易对列表
      symbols: [
        'BTCUSDT', 'ETHUSDT', 'BNBUSDT', 'ADAUSDT', 'XRPUSDT',
        'SOLUSDT', 'DOTUSDT', 'DOGEUSDT', 'AVAXUSDT', 'SHIBUSDT',
        'MATICUSDT', 'LTCUSDT', 'UNIUSDT', 'LINKUSDT', 'ATOMUSDT',
        'ETCUSDT', 'XLMUSDT', 'BCHUSDT', 'FILUSDT', 'TRXUSDT'
      ],
      
      // 支持的时间周期
      timeframes: ['1h', '4h', '1d'],
      
      // 支持的交易所
      exchanges: ['binance', 'okx'],
      
      // 每次获取的K线数量
      batchSize: 500,
      
      // 收集间隔（分钟）
      collectionInterval: 60, // 每小时收集一次
      
      // 是否启用自动收集
      autoCollection: true
    };
  }

  /**
   * 启动数据收集服务
   */
  async start() {
    if (this.isRunning) {
      console.log('数据收集服务已在运行');
      return;
    }

    console.log('启动数据收集服务...');
    this.isRunning = true;

    if (this.config.autoCollection) {
      // 立即执行一次数据收集
      this.collectAllData().catch(error => {
        console.error('初始数据收集失败:', error);
      });

      // 设置定时任务
      this.scheduleDataCollection();
    }

    console.log('数据收集服务启动完成');
  }

  /**
   * 停止数据收集服务
   */
  stop() {
    console.log('停止数据收集服务...');
    this.isRunning = false;

    // 取消所有定时任务
    for (const [jobName, job] of this.jobs) {
      job.cancel();
      console.log(`取消定时任务: ${jobName}`);
    }
    this.jobs.clear();

    console.log('数据收集服务已停止');
  }

  /**
   * 设置定时任务
   */
  scheduleDataCollection() {
    // 每小时的第5分钟执行数据收集
    const cronExpression = `5 */${this.config.collectionInterval} * * *`;
    
    const job = schedule.scheduleJob('dataCollection', cronExpression, async () => {
      console.log('定时数据收集开始...');
      try {
        await this.collectAllData();
        console.log('定时数据收集完成');
      } catch (error) {
        console.error('定时数据收集失败:', error);
      }
    });

    this.jobs.set('dataCollection', job);
    console.log(`数据收集定时任务已设置: ${cronExpression}`);
  }

  /**
   * 收集所有配置的数据
   */
  async collectAllData() {
    console.log('开始收集所有K线数据...');
    
    const startTime = Date.now();
    let totalCollected = 0;
    let totalErrors = 0;

    for (const exchange of this.config.exchanges) {
      for (const symbol of this.config.symbols) {
        for (const timeframe of this.config.timeframes) {
          try {
            const collected = await this.collectKlineData(exchange, symbol, timeframe);
            totalCollected += collected;
            
            // 添加延迟避免API限制
            await this.sleep(100);
          } catch (error) {
            console.error(`收集失败 ${exchange} ${symbol} ${timeframe}:`, error.message);
            totalErrors++;
          }
        }
      }
    }

    const duration = Date.now() - startTime;
    console.log(`数据收集完成: 收集 ${totalCollected} 条记录，${totalErrors} 个错误，耗时 ${duration}ms`);
  }

  /**
   * 收集指定交易所、交易对、时间周期的K线数据
   * @param {string} exchange 交易所
   * @param {string} symbol 交易对
   * @param {string} timeframe 时间周期
   * @returns {Promise<number>} 收集的数据条数
   */
  async collectKlineData(exchange, symbol, timeframe) {
    try {
      console.log(`收集K线数据: ${exchange} ${symbol} ${timeframe}`);

      // 获取最新的时间戳，用于增量更新
      const latestTimestamp = await klineDataService.getLatestTimestamp(exchange, symbol, timeframe);
      
      let klines;
      if (exchange === 'binance') {
        // 币安不需要用户凭证获取公开数据
        klines = await klineDataService.getKlineDataFromAPI(exchange, symbol, timeframe, this.config.batchSize);
      } else if (exchange === 'okx') {
        // OKX可以使用公开API获取K线数据，不需要用户凭证
        try {
          // 尝试使用公开API
          klines = await this.getOKXPublicKlines(symbol, timeframe, this.config.batchSize);
        } catch (error) {
          console.warn(`OKX公开API获取失败，跳过: ${error.message}`);
          return 0;
        }
      }

      if (!klines || klines.length === 0) {
        console.log(`没有获取到新的K线数据: ${exchange} ${symbol} ${timeframe}`);
        return 0;
      }

      // 如果有最新时间戳，只保留更新的数据
      let newKlines = klines;
      if (latestTimestamp) {
        newKlines = klines.filter(kline => kline.timestamp > latestTimestamp);
      }

      if (newKlines.length === 0) {
        console.log(`没有新的K线数据: ${exchange} ${symbol} ${timeframe}`);
        return 0;
      }

      // 存储到数据库
      const result = await klineDataService.storeKlineData(exchange, symbol, timeframe, newKlines);
      console.log(`存储完成: ${exchange} ${symbol} ${timeframe}, 新增 ${result.insertedCount} 条`);
      
      return result.insertedCount;
    } catch (error) {
      console.error(`收集K线数据失败 ${exchange} ${symbol} ${timeframe}:`, error);
      throw error;
    }
  }

  /**
   * 获取OKX公开K线数据
   * @param {string} symbol 交易对
   * @param {string} timeframe 时间周期
   * @param {number} limit 限制数量
   * @returns {Promise<Array>} K线数据
   */
  async getOKXPublicKlines(symbol, timeframe, limit) {
    // 这里可以使用OKX的公开API，不需要用户凭证
    // 暂时跳过OKX的自动收集，因为需要用户凭证
    throw new Error('OKX自动收集暂未实现，需要用户API凭证');
  }

  /**
   * 手动触发数据收集
   * @param {string} exchange 交易所（可选）
   * @param {string} symbol 交易对（可选）
   * @param {string} timeframe 时间周期（可选）
   */
  async manualCollect(exchange = null, symbol = null, timeframe = null) {
    console.log('手动触发数据收集...');
    
    const exchanges = exchange ? [exchange] : this.config.exchanges;
    const symbols = symbol ? [symbol] : this.config.symbols;
    const timeframes = timeframe ? [timeframe] : this.config.timeframes;

    let totalCollected = 0;
    let totalErrors = 0;

    for (const ex of exchanges) {
      for (const sym of symbols) {
        for (const tf of timeframes) {
          try {
            const collected = await this.collectKlineData(ex, sym, tf);
            totalCollected += collected;
          } catch (error) {
            console.error(`手动收集失败 ${ex} ${sym} ${tf}:`, error.message);
            totalErrors++;
          }
        }
      }
    }

    console.log(`手动收集完成: 收集 ${totalCollected} 条记录，${totalErrors} 个错误`);
    return { collected: totalCollected, errors: totalErrors };
  }

  /**
   * 更新配置
   * @param {Object} newConfig 新配置
   */
  updateConfig(newConfig) {
    this.config = { ...this.config, ...newConfig };
    console.log('数据收集配置已更新:', this.config);

    // 如果服务正在运行，重新设置定时任务
    if (this.isRunning) {
      this.stop();
      this.start();
    }
  }

  /**
   * 获取服务状态
   */
  getStatus() {
    return {
      isRunning: this.isRunning,
      config: this.config,
      activeJobs: Array.from(this.jobs.keys())
    };
  }

  /**
   * 延迟函数
   * @param {number} ms 毫秒
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

module.exports = new DataCollectionService();
