const schedule = require('node-schedule');
const klineDataService = require('./klineDataService');

/**
 * 数据收集服务
 * 负责定期从交易所获取K线数据并存储到数据库
 */
class DataCollectionService {
  constructor() {
    this.jobs = new Map(); // 存储定时任务
    this.isRunning = false;

    // 默认配置
    this.config = {
      // 支持的交易对列表
      symbols: [
        'BTCUSDT', 'ETHUSDT', 'BNBUSDT', 'ADAUSDT', 'XRPUSDT',
        'SOLUSDT', 'DOTUSDT', 'DOGEUSDT', 'AVAXUSDT', 'SHIBUSDT',
        'MATICUSDT', 'LTCUSDT', 'UNIUSDT', 'LINKUSDT', 'ATOMUSDT',
        'ETCUSDT', 'XLMUSDT', 'BCHUSDT', 'FILUSDT', 'TRXUSDT'
      ],

      // 支持的时间周期
      timeframes: ['1h', '4h', '1d'],

      // 支持的交易所
      exchanges: ['binance', 'okx'],

      // 每次获取的K线数量
      batchSize: 500,

      // 收集间隔（分钟）
      collectionInterval: 60, // 每小时收集一次

      // 是否启用自动收集
      autoCollection: true
    };
  }

  /**
   * 启动数据收集服务
   */
  async start() {
    if (this.isRunning) {
      console.log('数据收集服务已在运行');
      return;
    }

    console.log('启动数据收集服务...');
    this.isRunning = true;

    if (this.config.autoCollection) {
      // 立即执行一次数据收集
      this.collectAllData().catch(error => {
        console.error('初始数据收集失败:', error);
      });

      // 设置定时任务
      this.scheduleDataCollection();
    }

    console.log('数据收集服务启动完成');
  }

  /**
   * 停止数据收集服务
   */
  stop() {
    console.log('停止数据收集服务...');
    this.isRunning = false;

    // 取消所有定时任务
    for (const [jobName, job] of this.jobs) {
      job.cancel();
      console.log(`取消定时任务: ${jobName}`);
    }
    this.jobs.clear();

    console.log('数据收集服务已停止');
  }

  /**
   * 设置定时任务
   */
  scheduleDataCollection() {
    // 每小时的第5分钟执行数据收集
    const cronExpression = `5 */${this.config.collectionInterval} * * *`;

    const job = schedule.scheduleJob('dataCollection', cronExpression, async () => {
      console.log('定时数据收集开始...');
      try {
        await this.collectAllData();
        console.log('定时数据收集完成');
      } catch (error) {
        console.error('定时数据收集失败:', error);
      }
    });

    this.jobs.set('dataCollection', job);
    console.log(`数据收集定时任务已设置: ${cronExpression}`);
  }

  /**
   * 收集所有配置的数据
   */
  async collectAllData() {
    console.log('开始收集所有K线数据...');

    const startTime = Date.now();
    let totalCollected = 0;
    let totalErrors = 0;

    for (const exchange of this.config.exchanges) {
      for (const symbol of this.config.symbols) {
        for (const timeframe of this.config.timeframes) {
          try {
            const collected = await this.collectKlineData(exchange, symbol, timeframe);
            totalCollected += collected;

            // 添加延迟避免API限制
            await this.sleep(100);
          } catch (error) {
            console.error(`收集失败 ${exchange} ${symbol} ${timeframe}:`, error.message);
            totalErrors++;
          }
        }
      }
    }

    const duration = Date.now() - startTime;
    console.log(`数据收集完成: 收集 ${totalCollected} 条记录，${totalErrors} 个错误，耗时 ${duration}ms`);
  }

  /**
   * 收集指定交易所、交易对、时间周期的K线数据
   * @param {string} exchange 交易所
   * @param {string} symbol 交易对
   * @param {string} timeframe 时间周期
   * @returns {Promise<number>} 收集的数据条数
   */
  async collectKlineData(exchange, symbol, timeframe) {
    try {
      console.log(`收集K线数据: ${exchange} ${symbol} ${timeframe}`);

      // 获取最新的时间戳，用于增量更新
      const latestTimestamp = await klineDataService.getLatestTimestamp(exchange, symbol, timeframe);

      let klines;
      if (exchange === 'binance') {
        // 币安不需要用户凭证获取公开数据
        klines = await klineDataService.getKlineDataFromAPI(exchange, symbol, timeframe, this.config.batchSize);
      } else if (exchange === 'okx') {
        // OKX可以使用公开API获取K线数据，不需要用户凭证
        try {
          // 尝试使用公开API
          klines = await this.getOKXPublicKlines(symbol, timeframe, this.config.batchSize);
        } catch (error) {
          console.warn(`OKX公开API获取失败，跳过: ${error.message}`);
          return 0;
        }
      }

      if (!klines || klines.length === 0) {
        console.log(`没有获取到新的K线数据: ${exchange} ${symbol} ${timeframe}`);
        return 0;
      }

      // 如果有最新时间戳，只保留更新的数据
      let newKlines = klines;
      if (latestTimestamp) {
        newKlines = klines.filter(kline => kline.timestamp > latestTimestamp);
      }

      if (newKlines.length === 0) {
        console.log(`没有新的K线数据: ${exchange} ${symbol} ${timeframe}`);
        return 0;
      }

      // 存储到数据库
      const result = await klineDataService.storeKlineData(exchange, symbol, timeframe, newKlines);
      console.log(`存储完成: ${exchange} ${symbol} ${timeframe}, 新增 ${result.insertedCount} 条`);

      return result.insertedCount;
    } catch (error) {
      console.error(`收集K线数据失败 ${exchange} ${symbol} ${timeframe}:`, error);
      throw error;
    }
  }

  /**
   * 获取OKX公开K线数据
   * @param {string} symbol 交易对
   * @param {string} timeframe 时间周期
   * @param {number} limit 限制数量
   * @returns {Promise<Array>} K线数据
   */
  async getOKXPublicKlines(symbol, timeframe, limit) {
    // 这里可以使用OKX的公开API，不需要用户凭证
    // 暂时跳过OKX的自动收集，因为需要用户凭证
    throw new Error('OKX自动收集暂未实现，需要用户API凭证');
  }

  /**
   * 手动触发数据收集
   * @param {string} exchange 交易所（可选）
   * @param {string} symbol 交易对（可选）
   * @param {string} timeframe 时间周期（可选）
   */
  async manualCollect(exchange = null, symbol = null, timeframe = null) {
    console.log('手动触发数据收集...');

    const exchanges = exchange ? [exchange] : this.config.exchanges;
    const symbols = symbol ? [symbol] : this.config.symbols;
    const timeframes = timeframe ? [timeframe] : this.config.timeframes;

    let totalCollected = 0;
    let totalErrors = 0;

    for (const ex of exchanges) {
      for (const sym of symbols) {
        for (const tf of timeframes) {
          try {
            const collected = await this.collectKlineData(ex, sym, tf);
            totalCollected += collected;
          } catch (error) {
            console.error(`手动收集失败 ${ex} ${sym} ${tf}:`, error.message);
            totalErrors++;
          }
        }
      }
    }

    console.log(`手动收集完成: 收集 ${totalCollected} 条记录，${totalErrors} 个错误`);
    return { collected: totalCollected, errors: totalErrors };
  }

  /**
   * 更新配置
   * @param {Object} newConfig 新配置
   */
  updateConfig(newConfig) {
    this.config = { ...this.config, ...newConfig };
    console.log('数据收集配置已更新:', this.config);

    // 如果服务正在运行，重新设置定时任务
    if (this.isRunning) {
      this.stop();
      this.start();
    }
  }

  /**
   * 获取服务状态
   */
  getStatus() {
    return {
      isRunning: this.isRunning,
      config: this.config,
      activeJobs: Array.from(this.jobs.keys())
    };
  }

  /**
   * 批量收集历史K线数据
   * @param {string} exchange 交易所
   * @param {string} symbol 交易对
   * @param {string} timeframe 时间周期
   * @param {string} startDate 开始日期 (YYYY-MM-DD)
   * @param {string} endDate 结束日期 (YYYY-MM-DD)
   * @returns {Promise<number>} 收集的数据条数
   */
  async collectHistoricalData(exchange, symbol, timeframe, startDate, endDate) {
    try {
      console.log(`📊 开始收集历史K线数据: ${exchange} ${symbol} ${timeframe} ${startDate} 到 ${endDate}`);

      const start = new Date(startDate).getTime();
      const end = new Date(endDate).getTime();

      if (start >= end) {
        throw new Error('开始日期必须早于结束日期');
      }

      // 计算需要的数据量
      const timeframeMs = this.getTimeframeMs(timeframe);
      const totalPeriods = Math.ceil((end - start) / timeframeMs);

      console.log(`📊 预计需要收集 ${totalPeriods} 条历史数据`);

      let totalCollected = 0;
      let currentEnd = end;
      const batchSize = 1000; // 每批次获取1000条

      // 分批次收集历史数据（从最新往历史收集）
      while (currentEnd > start && totalCollected < totalPeriods) {
        try {
          console.log(`📊 收集批次: ${new Date(currentEnd)} 往前 ${batchSize} 条`);

          let klines;
          if (exchange === 'binance') {
            // 使用币安公开API获取历史数据
            klines = await this.getBinanceHistoricalKlines(symbol, timeframe, batchSize, currentEnd);
          } else if (exchange === 'okx') {
            console.warn('OKX历史数据收集暂未实现');
            break;
          }

          if (!klines || klines.length === 0) {
            console.log('没有更多历史数据可收集');
            break;
          }

          // 过滤出指定时间范围内的数据
          const filteredKlines = klines.filter(kline =>
            kline.timestamp >= start && kline.timestamp <= end
          );

          if (filteredKlines.length === 0) {
            console.log('当前批次没有符合时间范围的数据');
            break;
          }

          // 存储到数据库
          const result = await klineDataService.storeKlineData(exchange, symbol, timeframe, filteredKlines);
          const inserted = result.insertedCount || 0;
          totalCollected += inserted;

          console.log(`📊 批次存储完成: 新增 ${inserted} 条，总计 ${totalCollected} 条`);

          // 更新下次收集的结束时间
          const oldestTimestamp = Math.min(...filteredKlines.map(k => k.timestamp));
          currentEnd = oldestTimestamp - timeframeMs;

          // 添加延迟避免API限制
          await this.sleep(200);

        } catch (batchError) {
          console.error(`批次收集失败: ${batchError.message}`);
          break;
        }
      }

      console.log(`📊 历史数据收集完成: ${exchange} ${symbol} ${timeframe}, 总计 ${totalCollected} 条`);
      return totalCollected;

    } catch (error) {
      console.error(`收集历史数据失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 获取币安历史K线数据
   * @param {string} symbol 交易对
   * @param {string} timeframe 时间周期
   * @param {number} limit 限制数量
   * @param {number} endTime 结束时间戳
   * @returns {Promise<Array>} K线数据
   */
  async getBinanceHistoricalKlines(symbol, timeframe, limit, endTime) {
    try {
      // 计算开始时间（往前推limit个周期）
      const timeframeMs = this.getTimeframeMs(timeframe);
      const startTime = endTime - (limit * timeframeMs);

      console.log(`📊 获取币安历史K线: ${symbol} ${timeframe} ${limit}条, 时间范围: ${new Date(startTime)} 到 ${new Date(endTime)}`);

      // 使用binancePythonTradeService获取历史数据
      const binancePythonTradeService = require('./binancePythonTradeService');
      const result = await binancePythonTradeService.getBinanceHistoricalKlines(symbol, timeframe, limit, startTime, endTime);

      if (result.success && result.klines) {
        return result.klines;
      } else {
        throw new Error(result.error || '获取历史K线数据失败');
      }
    } catch (error) {
      console.error(`获取币安历史K线失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 获取时间周期对应的毫秒数
   * @param {string} timeframe 时间周期
   * @returns {number} 毫秒数
   */
  getTimeframeMs(timeframe) {
    const timeframes = {
      '1m': 60 * 1000,
      '3m': 3 * 60 * 1000,
      '5m': 5 * 60 * 1000,
      '15m': 15 * 60 * 1000,
      '30m': 30 * 60 * 1000,
      '1h': 60 * 60 * 1000,
      '2h': 2 * 60 * 60 * 1000,
      '4h': 4 * 60 * 60 * 1000,
      '6h': 6 * 60 * 60 * 1000,
      '12h': 12 * 60 * 60 * 1000,
      '1d': 24 * 60 * 60 * 1000,
      '1w': 7 * 24 * 60 * 60 * 1000,
      '1M': 30 * 24 * 60 * 60 * 1000
    };
    return timeframes[timeframe] || 60 * 60 * 1000; // 默认1小时
  }

  /**
   * 延迟函数
   * @param {number} ms 毫秒
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

module.exports = new DataCollectionService();
