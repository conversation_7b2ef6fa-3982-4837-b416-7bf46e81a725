const klineDataService = require('./klineDataService');
const binancePythonTradeService = require('./binancePythonTradeService');

/**
 * 历史数据收集器
 * 负责批量收集从2024年8月1日开始的历史K线数据
 */
class HistoricalDataCollector {
  constructor() {
    this.isRunning = false;
    this.config = {
      // 数据收集起始日期
      startDate: '2024-08-01',
      
      // 支持的交易对
      symbols: [
        'BTCUSDT', 'ETHUSDT', 'BNBUSDT', 'ADAUSDT', 'XRPUSDT',
        'SOLUSDT', 'DOTUSDT', 'DOGEUSDT', 'AVAXUSDT', 'SHIBUSDT',
        'MATICUSDT', 'LTCUSDT', 'UNIUSDT', 'LINKUSDT', 'ATOMUSDT'
      ],
      
      // 支持的时间周期
      timeframes: ['1h', '4h', '1d'],
      
      // 每批次获取的数量
      batchSize: 1000,
      
      // 批次间延迟（毫秒）
      batchDelay: 500,
      
      // 每月数据收集间隔（毫秒）
      monthDelay: 2000
    };
  }

  /**
   * 开始历史数据收集
   * @param {Object} options 收集选项
   */
  async startCollection(options = {}) {
    if (this.isRunning) {
      console.log('历史数据收集已在运行中');
      return;
    }

    this.isRunning = true;
    console.log('🚀 开始历史数据收集...');

    try {
      const config = { ...this.config, ...options };
      
      // 计算需要收集的月份范围
      const startDate = new Date(config.startDate);
      const currentDate = new Date();
      const months = this.getMonthsRange(startDate, currentDate);
      
      console.log(`📅 需要收集 ${months.length} 个月的数据，从 ${config.startDate} 到 ${currentDate.toISOString().slice(0, 7)}`);

      let totalCollected = 0;
      let totalErrors = 0;

      // 按月份收集数据
      for (const month of months) {
        if (!this.isRunning) {
          console.log('收集被中断');
          break;
        }

        console.log(`\n📊 开始收集 ${month.year}-${month.month.toString().padStart(2, '0')} 的数据...`);
        
        try {
          const monthResult = await this.collectMonthData(month, config);
          totalCollected += monthResult.collected;
          totalErrors += monthResult.errors;
          
          console.log(`✅ ${month.year}-${month.month.toString().padStart(2, '0')} 收集完成: ${monthResult.collected} 条数据，${monthResult.errors} 个错误`);
          
          // 月份间延迟
          if (months.indexOf(month) < months.length - 1) {
            await this.sleep(config.monthDelay);
          }
        } catch (error) {
          console.error(`❌ ${month.year}-${month.month.toString().padStart(2, '0')} 收集失败:`, error.message);
          totalErrors++;
        }
      }

      console.log(`\n🎉 历史数据收集完成！`);
      console.log(`📊 总计收集: ${totalCollected} 条数据`);
      console.log(`❌ 总计错误: ${totalErrors} 个`);

    } catch (error) {
      console.error('历史数据收集失败:', error);
    } finally {
      this.isRunning = false;
    }
  }

  /**
   * 收集指定月份的数据
   * @param {Object} month 月份信息 {year, month}
   * @param {Object} config 配置
   * @returns {Promise<Object>} 收集结果
   */
  async collectMonthData(month, config) {
    const startTime = new Date(month.year, month.month - 1, 1).getTime();
    const endTime = new Date(month.year, month.month, 0, 23, 59, 59, 999).getTime();
    
    let collected = 0;
    let errors = 0;

    for (const symbol of config.symbols) {
      for (const timeframe of config.timeframes) {
        if (!this.isRunning) break;

        try {
          console.log(`  📈 收集 ${symbol} ${timeframe}...`);
          
          const result = await this.collectSymbolData(
            'binance', 
            symbol, 
            timeframe, 
            startTime, 
            endTime, 
            config.batchSize
          );
          
          collected += result;
          console.log(`    ✅ ${symbol} ${timeframe}: ${result} 条`);
          
          // 批次间延迟
          await this.sleep(config.batchDelay);
        } catch (error) {
          console.error(`    ❌ ${symbol} ${timeframe}: ${error.message}`);
          errors++;
        }
      }
    }

    return { collected, errors };
  }

  /**
   * 收集指定交易对的数据
   * @param {string} exchange 交易所
   * @param {string} symbol 交易对
   * @param {string} timeframe 时间周期
   * @param {number} startTime 开始时间戳
   * @param {number} endTime 结束时间戳
   * @param {number} batchSize 批次大小
   * @returns {Promise<number>} 收集的数据条数
   */
  async collectSymbolData(exchange, symbol, timeframe, startTime, endTime, batchSize) {
    try {
      // 检查数据库中是否已有该时间段的数据
      const existingData = await klineDataService.getKlineDataFromDB(
        exchange, symbol, timeframe, startTime, endTime, 1
      );
      
      if (existingData.length > 0) {
        console.log(`    ⏭️  ${symbol} ${timeframe} 数据已存在，跳过`);
        return 0;
      }

      // 从API获取数据
      const result = await binancePythonTradeService.getBinanceHistoricalKlines(
        symbol, timeframe, batchSize, startTime, endTime
      );

      if (!result.success || !result.klines || result.klines.length === 0) {
        throw new Error(result.error || '获取数据失败');
      }

      // 过滤时间范围
      const filteredKlines = result.klines.filter(kline => 
        kline.timestamp >= startTime && kline.timestamp <= endTime
      );

      if (filteredKlines.length === 0) {
        return 0;
      }

      // 存储到数据库
      const storeResult = await klineDataService.storeKlineData(
        exchange, symbol, timeframe, filteredKlines
      );

      return storeResult.insertedCount || 0;
    } catch (error) {
      throw new Error(`收集 ${symbol} ${timeframe} 数据失败: ${error.message}`);
    }
  }

  /**
   * 获取月份范围
   * @param {Date} startDate 开始日期
   * @param {Date} endDate 结束日期
   * @returns {Array} 月份数组
   */
  getMonthsRange(startDate, endDate) {
    const months = [];
    const current = new Date(startDate.getFullYear(), startDate.getMonth(), 1);
    const end = new Date(endDate.getFullYear(), endDate.getMonth(), 1);

    while (current <= end) {
      months.push({
        year: current.getFullYear(),
        month: current.getMonth() + 1
      });
      current.setMonth(current.getMonth() + 1);
    }

    return months;
  }

  /**
   * 停止收集
   */
  stop() {
    console.log('正在停止历史数据收集...');
    this.isRunning = false;
  }

  /**
   * 获取收集状态
   */
  getStatus() {
    return {
      isRunning: this.isRunning,
      config: this.config
    };
  }

  /**
   * 延迟函数
   * @param {number} ms 毫秒
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

module.exports = new HistoricalDataCollector();
